/* Enhanced Banking System Styles */

/* Global Variables */
:root {
    --primary-color: #1a237e;
    --primary-light: #3949ab;
    --primary-dark: #0d1642;
    --secondary-color: #3f51b5;
    --secondary-light: #e8eaf6;
    --accent-color: #ff4081;
    --text-primary: #333;
    --text-secondary: #757575;
    --text-light: #fff;
    --success-color: #4caf50;
    --success-light: #e8f5e9;
    --warning-color: #ff9800;
    --warning-light: #fff8e1;
    --error-color: #f44336;
    --error-light: #ffebee;
    --info-color: #2196f3;
    --info-light: #e3f2fd;
    --border-color: #e0e0e0;
    --background-color: #f5f5f5;
    --card-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
    --transition-speed: 0.3s;
    --border-radius: 8px;
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --spacing-xl: 30px;
}

/* Base Styles Enhancement */
html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-color);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    /* Using CSS pattern instead of image file */
    background-image:
        radial-gradient(circle at 25px 25px, rgba(63, 81, 181, 0.15) 2px, transparent 0),
        radial-gradient(circle at 75px 75px, rgba(63, 81, 181, 0.1) 2px, transparent 0);
    background-size: 100px 100px;
    background-attachment: fixed;
}

.container {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: var(--spacing-lg);
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: var(--card-shadow);
    border-radius: var(--border-radius);
    flex: 1;
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    line-height: 1.3;
}

h1 {
    font-size: 2.2rem;
    font-weight: 700;
}

h2 {
    font-size: 1.8rem;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

h3 {
    font-size: 1.4rem;
}

p {
    margin-bottom: var(--spacing-md);
    line-height: 1.7;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-speed), background-color var(--transition-speed);
    position: relative;
}

a:hover {
    color: var(--primary-light);
}

a:focus {
    outline: 2px solid var(--primary-light);
    outline-offset: 2px;
}

/* Enhanced Header */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-lg);
    position: relative;
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    opacity: 0.1;
    z-index: -1;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    /* Using CSS pattern instead of image file */
    background-color: var(--primary-color);
    background-image:
        linear-gradient(30deg, rgba(255, 255, 255, 0.2) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.2) 87.5%, rgba(255, 255, 255, 0.2)),
        linear-gradient(150deg, rgba(255, 255, 255, 0.2) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.2) 87.5%, rgba(255, 255, 255, 0.2)),
        linear-gradient(30deg, rgba(255, 255, 255, 0.2) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.2) 87.5%, rgba(255, 255, 255, 0.2)),
        linear-gradient(150deg, rgba(255, 255, 255, 0.2) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.2) 87.5%, rgba(255, 255, 255, 0.2));
    background-size: 40px 70px;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-info p {
    margin-right: var(--spacing-md);
    margin-bottom: 0;
    font-weight: 500;
}

.logout-btn {
    background-color: var(--error-color);
    color: var(--text-light);
    padding: 8px 15px;
    border-radius: 4px;
    font-weight: bold;
    transition: background-color var(--transition-speed), transform var(--transition-speed);
}

.logout-btn:hover {
    background-color: #d32f2f;
    text-decoration: none;
    transform: translateY(-2px);
}

.logout-btn:active {
    transform: translateY(0);
}

/* Enhanced Navigation */
nav {
    margin-bottom: var(--spacing-xl);
}

nav ul {
    display: flex;
    list-style: none;
    background-color: var(--primary-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

nav li {
    flex: 1;
    text-align: center;
    position: relative;
}

nav a {
    display: block;
    color: var(--text-light);
    padding: var(--spacing-md);
    transition: background-color var(--transition-speed);
    font-weight: 500;
}

nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background-color: var(--accent-color);
    transition: width var(--transition-speed), left var(--transition-speed);
}

nav a:hover {
    background-color: var(--primary-light);
    text-decoration: none;
    color: var(--text-light);
}

nav a:hover::after {
    width: 100%;
    left: 0;
}

nav a.active {
    background-color: var(--primary-light);
    font-weight: bold;
}

nav a.active::after {
    width: 100%;
    left: 0;
}

/* Enhanced Main Content */
main {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
}

section {
    margin-bottom: var(--spacing-xl);
    position: relative;
}

section:last-child {
    margin-bottom: 0;
}

/* Enhanced Forms */
.form-group {
    margin-bottom: var(--spacing-lg);
}

label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
}

input, select, textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 16px;
    transition: border-color var(--transition-speed), box-shadow var(--transition-speed);
    background-color: #fff;
}

input:focus, select:focus, textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(26, 35, 126, 0.2);
    outline: none;
}

input::placeholder {
    color: #aaa;
}

button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: background-color var(--transition-speed), transform var(--transition-speed);
}

button:hover {
    background-color: var(--primary-light);
    transform: translateY(-2px);
}

button:active {
    transform: translateY(0);
}

button:focus {
    outline: 2px solid var(--primary-light);
    outline-offset: 2px;
}

.btn {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 10px 15px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: bold;
    margin-right: 10px;
    transition: background-color var(--transition-speed), transform var(--transition-speed);
}

.btn:hover {
    background-color: var(--primary-light);
    text-decoration: none;
    transform: translateY(-2px);
}

.btn:active {
    transform: translateY(0);
}

.btn:focus {
    outline: 2px solid var(--primary-light);
    outline-offset: 2px;
}

/* Enhanced Footer */
footer {
    text-align: center;
    padding: var(--spacing-lg) 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    background-color: var(--primary-color);
    color: var(--text-light);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    margin-top: auto;
}

/* Responsive Enhancements */
@media (max-width: 1200px) {
    .container {
        max-width: 95%;
    }
}

@media (max-width: 992px) {
    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.6rem;
    }

    h3 {
        font-size: 1.3rem;
    }

    .container {
        padding: var(--spacing-md);
    }

    main {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    header {
        flex-direction: column;
        text-align: center;
        padding-bottom: var(--spacing-md);
    }

    .user-info {
        margin-top: var(--spacing-md);
        flex-direction: column;
    }

    .user-info p {
        margin-right: 0;
        margin-bottom: var(--spacing-sm);
    }

    nav ul {
        flex-direction: column;
    }

    nav li {
        margin-bottom: 1px;
    }

    nav a::after {
        display: none;
    }

    .btn {
        display: block;
        margin-bottom: var(--spacing-sm);
        text-align: center;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 1.8rem;
    }

    h2 {
        font-size: 1.4rem;
    }

    h3 {
        font-size: 1.2rem;
    }

    .container {
        padding: var(--spacing-sm);
    }

    main {
        padding: var(--spacing-md);
    }
}

/* Enhanced Card Styles */
.card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    overflow: hidden;
    position: relative;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.card-header {
    margin: calc(-1 * var(--spacing-lg));
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-lg);
    background-color: var(--secondary-light);
    border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
    margin-bottom: 0;
}

.card-body {
    position: relative;
}

.card-footer {
    margin: 0 calc(-1 * var(--spacing-lg));
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background-color: var(--secondary-light);
    border-top: 1px solid var(--border-color);
}

/* Enhanced Table Styles */
.table-container {
    overflow-x: auto;
    margin-bottom: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background-color: var(--secondary-light);
    color: var(--primary-color);
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
}

tr:hover {
    background-color: rgba(232, 234, 246, 0.3);
}

/* Enhanced Status Badges */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background-color: var(--success-light);
    color: var(--success-color);
}

.status-pending {
    background-color: var(--warning-light);
    color: var(--warning-color);
}

.status-suspended {
    background-color: var(--error-light);
    color: var(--error-color);
}

/* Enhanced Message Styles */
.message {
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
    position: relative;
    padding-left: 50px;
}

.message::before {
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5rem;
}

.error-message {
    background-color: var(--error-light);
    color: var(--error-color);
    border-left: 5px solid var(--error-color);
}

.error-message::before {
    content: '\\f06a'; /* exclamation-circle */
}

.success-message {
    background-color: var(--success-light);
    color: var(--success-color);
    border-left: 5px solid var(--success-color);
}

.success-message::before {
    content: '\\f058'; /* check-circle */
}

.info-message {
    background-color: var(--info-light);
    color: var(--info-color);
    border-left: 5px solid var(--info-color);
}

.info-message::before {
    content: '\\f05a'; /* info-circle */
}

.warning-message {
    background-color: var(--warning-light);
    color: var(--warning-color);
    border-left: 5px solid var(--warning-color);
}

.warning-message::before {
    content: '\\f071'; /* exclamation-triangle */
}

/* Enhanced Dashboard Styles */
.dashboard-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.welcome-card {
    grid-column: 1 / -1;
    background-color: var(--secondary-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--card-shadow);
    border-left: 5px solid var(--secondary-color);
    position: relative;
    overflow: hidden;
}

.welcome-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 100%;
    background-image:
        linear-gradient(45deg, rgba(63, 81, 181, 0.1) 25%, transparent 25%, transparent 75%, rgba(63, 81, 181, 0.1) 75%, rgba(63, 81, 181, 0.1)),
        linear-gradient(-45deg, rgba(63, 81, 181, 0.1) 25%, transparent 25%, transparent 75%, rgba(63, 81, 181, 0.1) 75%, rgba(63, 81, 181, 0.1));
    background-size: 20px 20px;
    background-position: 0 0, 10px 10px;
    opacity: 0.5;
}

.summary-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--card-shadow);
    text-align: center;
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.summary-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.summary-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Enhanced Login Styles */
.login-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin: var(--spacing-xl) 0;
}

.login-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: var(--spacing-xl);
    max-width: 500px;
    width: 100%;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

.login-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.login-form .form-group {
    margin-bottom: var(--spacing-lg);
}

.login-form label {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 500;
}

.login-form label i {
    margin-right: var(--spacing-sm);
    color: var(--secondary-color);
}

.login-form input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color var(--transition-speed), box-shadow var(--transition-speed);
}

.login-form input:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.2);
    outline: none;
}

.btn-login {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color var(--transition-speed), transform var(--transition-speed);
    display: flex;
    justify-content: center;
    align-items: center;
}

.btn-login:hover {
    background-color: var(--primary-light);
    transform: translateY(-2px);
}

.btn-login:active {
    transform: translateY(0);
}

.btn-login i {
    margin-right: var(--spacing-sm);
}

/* Responsive Adjustments for Login */
@media (max-width: 992px) {
    .login-container {
        grid-template-columns: 1fr;
    }

    .login-card {
        max-width: 100%;
    }
}
