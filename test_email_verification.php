<?php
/**
 * Test Script for Email Verification System
 * 
 * This script tests the email verification system by generating a token,
 * sending a test email, and validating the token.
 */

// Enable development mode
define('DEVELOPMENT_MODE', true);

// Include required files
require_once 'config/db_connect.php';
require_once 'includes/token_service.php';
require_once 'includes/email_service.php';

// Test email address
$test_email = '<EMAIL>';
$test_name = 'Test User';

// Output header
echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Email Verification Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Email Verification Test</h1>";

// Test 1: Generate a verification token
echo "<div class='section'>
    <h2>Test 1: Generate Verification Token</h2>";

$token = generate_verification_token();

if (!empty($token)) {
    echo "<p class='success'>Success! Generated token: <code>{$token}</code></p>";
} else {
    echo "<p class='error'>Error: Failed to generate token.</p>";
}

echo "</div>";

// Test 2: Send a verification email
echo "<div class='section'>
    <h2>Test 2: Send Verification Email</h2>";

$email_result = send_verification_email($test_email, $test_name, $token);

if ($email_result['status'] === 'success') {
    echo "<p class='success'>Success! Email would be sent to {$test_email}.</p>";
    
    // Show email templates
    $base_url = get_base_url();
    $verification_link = $base_url . 'verify.php?code=' . $token;
    
    echo "<h3>HTML Email Template:</h3>";
    echo "<pre>" . htmlspecialchars(get_verification_email_html_template($test_name, $verification_link)) . "</pre>";
    
    echo "<h3>Text Email Template:</h3>";
    echo "<pre>" . htmlspecialchars(get_verification_email_text_template($test_name, $verification_link)) . "</pre>";
} else {
    echo "<p class='error'>Error: {$email_result['message']}</p>";
}

echo "</div>";

// Test 3: Store and validate a token
echo "<div class='section'>
    <h2>Test 3: Store and Validate Token</h2>";

// Create a test customer if not exists
$check_sql = "SELECT CustomerID FROM customer WHERE Email = '{$test_email}'";
$check_result = $conn->query($check_sql);

if ($check_result->num_rows === 0) {
    $insert_sql = "INSERT INTO customer (Name, Email, Phone, Address, Status, IsVerified) 
                  VALUES ('{$test_name}', '{$test_email}', '1234567890', 'Test Address', 'pending', 0)";
    
    if ($conn->query($insert_sql)) {
        $customer_id = $conn->insert_id;
        echo "<p class='success'>Created test customer with ID: {$customer_id}</p>";
    } else {
        echo "<p class='error'>Error creating test customer: {$conn->error}</p>";
        $customer_id = 0;
    }
} else {
    $row = $check_result->fetch_assoc();
    $customer_id = $row['CustomerID'];
    echo "<p class='success'>Using existing test customer with ID: {$customer_id}</p>";
}

if ($customer_id > 0) {
    // Store token
    if (store_verification_token($conn, $customer_id, $token)) {
        echo "<p class='success'>Successfully stored token in database.</p>";
        
        // Validate token
        $validation_result = validate_verification_token($conn, $token);
        
        if ($validation_result['status'] === 'success') {
            echo "<p class='success'>Successfully validated token.</p>";
            echo "<pre>" . print_r($validation_result, true) . "</pre>";
            
            // Mark as verified
            if (mark_email_as_verified($conn, $customer_id)) {
                echo "<p class='success'>Successfully marked email as verified.</p>";
            } else {
                echo "<p class='error'>Error marking email as verified: {$conn->error}</p>";
            }
        } else {
            echo "<p class='error'>Error validating token: {$validation_result['message']}</p>";
            echo "<pre>" . print_r($validation_result, true) . "</pre>";
        }
    } else {
        echo "<p class='error'>Error storing token in database: {$conn->error}</p>";
    }
}

echo "</div>";

// Test 4: Check logs
echo "<div class='section'>
    <h2>Test 4: Check Logs</h2>";

$email_log_file = 'logs/email_log.txt';
$verification_log_file = 'logs/verification_log.txt';

if (file_exists($email_log_file)) {
    echo "<h3>Email Log:</h3>";
    echo "<pre>" . htmlspecialchars(file_get_contents($email_log_file)) . "</pre>";
} else {
    echo "<p class='error'>Email log file not found.</p>";
}

if (file_exists($verification_log_file)) {
    echo "<h3>Verification Log:</h3>";
    echo "<pre>" . htmlspecialchars(file_get_contents($verification_log_file)) . "</pre>";
} else {
    echo "<p class='error'>Verification log file not found.</p>";
}

echo "</div>";

// Output footer
echo "</div>
</body>
</html>";

// Close database connection
$conn->close();
?>
