<?php
session_start();

// Enable development mode for testing (set to false in production)
define('DEVELOPMENT_MODE', true);

// Redirect if already logged in
if (isset($_SESSION['customer_id'])) {
    header("Location: dashboard.php");
    exit();
} elseif (isset($_SESSION['admin_id'])) {
    header("Location: admin/dashboard.php");
    exit();
}

require_once 'config/db_connect.php';
require_once 'includes/token_service.php';
require_once 'includes/email_service.php';

// Get branches for dropdown
$branches_sql = "SELECT BranchID, BranchName, Location FROM branch";
$branches_result = $conn->query($branches_sql);

$branches = array();
if ($branches_result->num_rows > 0) {
    while($row = $branches_result->fetch_assoc()) {
        $branches[$row['BranchID']] = $row['BranchName'] . ' (' . $row['Location'] . ')';
    }
}

// Process signup form
$error = "";
$success = "";
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $name = $conn->real_escape_string($_POST['name']);
    $email = $conn->real_escape_string($_POST['email']);
    $phone = $conn->real_escape_string($_POST['phone']);
    $address = $conn->real_escape_string($_POST['address']);
    $branch_id = intval($_POST['branch_id']);
    $account_type = $conn->real_escape_string($_POST['account_type']);
    $initial_deposit = floatval($_POST['initial_deposit']);

    // Validate form data
    if (empty($name) || empty($email) || empty($phone) || empty($address)) {
        $error = "All fields are required";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } elseif ($initial_deposit < 500) {
        $error = "Initial deposit must be at least $500";
    } else {
        // Check if email already exists
        $check_email = "SELECT CustomerID FROM customer WHERE Email = '$email'";
        $email_result = $conn->query($check_email);

        if ($email_result->num_rows > 0) {
            $error = "Email already registered. Please use a different email.";
        } else {
            // Generate secure verification token
            $verification_token = generate_verification_token();

            // Calculate token expiry (24 hours from now)
            $token_expiry = date('Y-m-d H:i:s', strtotime('+24 hours'));

            // Start transaction
            $conn->begin_transaction();

            try {
                // Insert customer using prepared statement
                $insert_customer = $conn->prepare("INSERT INTO customer (Name, Phone, Email, Address, Status, VerificationCode, VerificationExpiry)
                                   VALUES (?, ?, ?, ?, 'pending', ?, ?)");
                $insert_customer->bind_param('ssssss', $name, $phone, $email, $address, $verification_token, $token_expiry);
                $insert_customer->execute();
                $customer_id = $conn->insert_id;
                $insert_customer->close();

                // Insert pending account request using prepared statement
                $insert_request = $conn->prepare("INSERT INTO pending_account (CustomerID, AccountType, BranchID, InitialDeposit)
                                  VALUES (?, ?, ?, ?)");
                $insert_request->bind_param('isid', $customer_id, $account_type, $branch_id, $initial_deposit);
                $insert_request->execute();
                $insert_request->close();

                // Commit transaction
                $conn->commit();

                // Send verification email
                $email_result = send_verification_email($email, $name, $verification_token);

                // Log verification activity
                $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                log_verification_activity('resend', $customer_id, $email, $email_result['status'], $ip_address, $user_agent);

                if ($email_result['status'] === 'success') {
                    $success = "Your account request has been submitted successfully! Please check your email to verify your account.";

                    // For development/testing purposes only - show verification link
                    if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
                        $base_url = get_base_url();
                        $verification_link = $base_url . 'verify.php?code=' . $verification_token;
                        $success .= "<div class='demo-verification-link'><strong>Demo:</strong> <a href='$verification_link'>Click here to verify your email</a></div>";
                    }

                    // Clear form data
                    $name = $email = $phone = $address = "";
                    $branch_id = $account_type = "";
                    $initial_deposit = 500;
                } else {
                    // Email sending failed, but account was created
                    $success = "Your account request has been submitted successfully! However, there was an issue sending the verification email. Please use the 'Resend Verification Email' option if you don't receive it.";
                }

            } catch (Exception $e) {
                // Rollback transaction on error
                $conn->rollback();
                $error = "Registration failed: " . $e->getMessage();
            }
        }
    }
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Sign Up</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/landing.css">
    <link rel="stylesheet" href="css/enhanced-style.css">
    <link rel="stylesheet" href="css/banking-patterns.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Banking System</h1>
            <nav class="top-nav">
                <ul>
                    <li><a href="index.php">Home</a></li>
                    <li><a href="customer_login.php">Customer Login</a></li>
                    <li><a href="admin/login.php">Admin Login</a></li>
                    <li><a href="signup.php" class="active">Sign Up</a></li>
                </ul>
            </nav>
        </header>

        <main>
            <section class="signup-section">
                <h2>Open a New Account</h2>

                <?php if (!empty($error)): ?>
                    <div class="error-message"><?php echo $error; ?></div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                    <div class="success-message"><?php echo $success; ?></div>
                <?php endif; ?>

                <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" id="signup-form">
                    <div class="form-section">
                        <h3>Personal Information</h3>

                        <div class="form-group">
                            <label for="name">Full Name:</label>
                            <input type="text" id="name" name="name" value="<?php echo isset($name) ? htmlspecialchars($name) : ''; ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="email">Email Address:</label>
                            <input type="email" id="email" name="email" value="<?php echo isset($email) ? htmlspecialchars($email) : ''; ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="phone">Phone Number:</label>
                            <input type="text" id="phone" name="phone" value="<?php echo isset($phone) ? htmlspecialchars($phone) : ''; ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="address">Address:</label>
                            <input type="text" id="address" name="address" value="<?php echo isset($address) ? htmlspecialchars($address) : ''; ?>" required>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>Account Information</h3>

                        <div class="form-group">
                            <label for="branch_id">Preferred Branch:</label>
                            <select id="branch_id" name="branch_id" required>
                                <option value="">Select Branch</option>
                                <?php foreach ($branches as $id => $name): ?>
                                    <option value="<?php echo $id; ?>" <?php echo (isset($branch_id) && $branch_id == $id) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="account_type">Account Type:</label>
                            <select id="account_type" name="account_type" required>
                                <option value="">Select Account Type</option>
                                <option value="Savings" <?php echo (isset($account_type) && $account_type == 'Savings') ? 'selected' : ''; ?>>Savings Account</option>
                                <option value="Checking" <?php echo (isset($account_type) && $account_type == 'Checking') ? 'selected' : ''; ?>>Checking Account</option>
                                <option value="Investment" <?php echo (isset($account_type) && $account_type == 'Investment') ? 'selected' : ''; ?>>Investment Account</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="initial_deposit">Initial Deposit Amount ($):</label>
                            <input type="number" id="initial_deposit" name="initial_deposit" min="500" step="0.01" value="<?php echo isset($initial_deposit) ? htmlspecialchars($initial_deposit) : '500'; ?>" required>
                            <small>Minimum deposit: $500</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Submit Application</button>
                    </div>

                    <div class="form-footer">
                        <p>Already have an account? <a href="customer_login.php">Login here</a></p>
                    </div>
                </form>
            </section>
        </main>

        <footer>
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Banking System</h3>
                    <p>Your trusted financial partner since 2025.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="customer_login.php">Customer Login</a></li>
                        <li><a href="admin/login.php">Admin Login</a></li>
                        <li><a href="signup.php">Sign Up</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Us</h3>
                    <p>Email: <EMAIL></p>
                    <p>Phone: (*************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
            </div>
        </footer>
    </div>

    <script src="js/script.js"></script>
</body>
</html>
