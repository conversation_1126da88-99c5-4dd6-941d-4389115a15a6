<?php
/**
 * Email Service for Banking System
 *
 * This file contains functions for sending emails using <PERSON><PERSON><PERSON>ail<PERSON>
 * and managing email templates.
 */

// Include PHPMailer classes
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\Exception;

// Load PHPMailer autoloader
require 'vendor/autoload.php';

// Email configuration constants
define('MAIL_HOST', 'smtp.example.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '<EMAIL>');
define('MAIL_PASSWORD', 'your_email_password');
define('MAIL_FROM_EMAIL', '<EMAIL>');
define('MAIL_FROM_NAME', 'Banking System');
define('MAIL_REPLY_TO', '<EMAIL>');
define('MAIL_DEBUG', 0); // 0 = off, 1 = client messages, 2 = client and server messages

/**
 * Send an email using PHPMailer
 *
 * @param string $to_email Recipient email address
 * @param string $to_name Recipient name
 * @param string $subject Email subject
 * @param string $html_body HTML email body
 * @param string $text_body Plain text email body (fallback)
 * @param array $attachments Optional array of attachments
 * @return array Result with status and message
 */
function send_email($to_email, $to_name, $subject, $html_body, $text_body, $attachments = []) {
    // In development mode, just log the email and return success
    if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
        // Log the email
        log_email_activity('success', $to_email, $subject, 'Development mode - email not actually sent');

        return [
            'status' => 'success',
            'message' => 'Email would be sent in production mode'
        ];
    }

    // Create a new PHPMailer instance
    $mail = new PHPMailer(true);

    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = MAIL_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = MAIL_USERNAME;
        $mail->Password = MAIL_PASSWORD;
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = MAIL_PORT;
        $mail->SMTPDebug = MAIL_DEBUG;

        // Recipients
        $mail->setFrom(MAIL_FROM_EMAIL, MAIL_FROM_NAME);
        $mail->addAddress($to_email, $to_name);
        $mail->addReplyTo(MAIL_REPLY_TO, MAIL_FROM_NAME);

        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $html_body;
        $mail->AltBody = $text_body;

        // Add attachments if any
        if (!empty($attachments)) {
            foreach ($attachments as $attachment) {
                if (isset($attachment['path']) && file_exists($attachment['path'])) {
                    $mail->addAttachment(
                        $attachment['path'],
                        isset($attachment['name']) ? $attachment['name'] : ''
                    );
                }
            }
        }

        // Send the email
        $mail->send();

        // Log successful email
        log_email_activity('success', $to_email, $subject);

        return [
            'status' => 'success',
            'message' => 'Email sent successfully'
        ];
    } catch (Exception $e) {
        // Log failed email
        log_email_activity('error', $to_email, $subject, $mail->ErrorInfo);

        return [
            'status' => 'error',
            'message' => 'Email could not be sent. Error: ' . $mail->ErrorInfo
        ];
    }
}

/**
 * Send a verification email to a new user
 *
 * @param string $to_email Recipient email address
 * @param string $to_name Recipient name
 * @param string $verification_code Verification code
 * @return array Result with status and message
 */
function send_verification_email($to_email, $to_name, $verification_code) {
    // Get the base URL
    $base_url = get_base_url();

    // Create verification link
    $verification_link = $base_url . 'verify.php?code=' . $verification_code;

    // Email subject
    $subject = 'Verify Your Banking System Account';

    // Get email templates
    $html_body = get_verification_email_html_template($to_name, $verification_link);
    $text_body = get_verification_email_text_template($to_name, $verification_link);

    // Send the email
    return send_email($to_email, $to_name, $subject, $html_body, $text_body);
}

/**
 * Get the HTML template for verification emails
 *
 * @param string $name Recipient name
 * @param string $verification_link Verification link
 * @return string HTML email template
 */
function get_verification_email_html_template($name, $verification_link) {
    $base_url = get_base_url();
    $current_year = date('Y');

    return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Banking System Account</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #1a237e;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            color: white;
            margin: 0;
        }
        .content {
            padding: 20px;
            background-color: #f9f9f9;
        }
        .footer {
            background-color: #f1f1f1;
            padding: 15px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        .button {
            display: inline-block;
            background-color: #1a237e;
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 4px;
            margin: 20px 0;
            font-weight: bold;
        }
        .verification-code {
            background-color: #e8eaf6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 20px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Banking System</h1>
        </div>
        <div class="content">
            <h2>Verify Your Email Address</h2>
            <p>Hello $name,</p>
            <p>Thank you for registering with Banking System. To complete your registration and verify your email address, please click the button below:</p>

            <div style="text-align: center;">
                <a href="$verification_link" class="button">Verify Email Address</a>
            </div>

            <p>If the button above doesn't work, you can copy and paste the following link into your browser:</p>
            <div class="verification-code">$verification_link</div>

            <p>This verification link will expire in 24 hours for security reasons.</p>

            <p>If you did not create an account with Banking System, please ignore this email.</p>

            <p>Thank you,<br>The Banking System Team</p>
        </div>
        <div class="footer">
            <p>&copy; $current_year Banking System. All rights reserved.</p>
            <p>This is an automated email, please do not reply.</p>
        </div>
    </div>
</body>
</html>
HTML;
}

/**
 * Get the plain text template for verification emails
 *
 * @param string $name Recipient name
 * @param string $verification_link Verification link
 * @return string Plain text email template
 */
function get_verification_email_text_template($name, $verification_link) {
    $current_year = date('Y');

    return <<<TEXT
BANKING SYSTEM - VERIFY YOUR EMAIL ADDRESS

Hello $name,

Thank you for registering with Banking System. To complete your registration and verify your email address, please visit the following link:

$verification_link

This verification link will expire in 24 hours for security reasons.

If you did not create an account with Banking System, please ignore this email.

Thank you,
The Banking System Team

© $current_year Banking System. All rights reserved.
This is an automated email, please do not reply.
TEXT;
}

/**
 * Log email activity for tracking and debugging
 *
 * @param string $status Status of the email (success/error)
 * @param string $recipient Recipient email address
 * @param string $subject Email subject
 * @param string $error_message Optional error message
 * @return bool Whether the log was successful
 */
function log_email_activity($status, $recipient, $subject, $error_message = '') {
    // Create logs directory if it doesn't exist
    $log_dir = __DIR__ . '/../logs';
    if (!file_exists($log_dir)) {
        mkdir($log_dir, 0755, true);
    }

    // Create log file path
    $log_file = $log_dir . '/email_log.txt';

    // Format log entry
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] STATUS: {$status} | RECIPIENT: {$recipient} | SUBJECT: {$subject}";

    if (!empty($error_message)) {
        $log_entry .= " | ERROR: {$error_message}";
    }

    $log_entry .= PHP_EOL;

    // Write to log file
    return file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX) !== false;
}

/**
 * Get the base URL of the application
 *
 * @return string Base URL
 */
function get_base_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];
    $script_name = $_SERVER['SCRIPT_NAME'];
    $path = dirname($script_name);
    $path = $path !== '/' ? $path . '/' : '/';

    return $protocol . $host . $path;
}
