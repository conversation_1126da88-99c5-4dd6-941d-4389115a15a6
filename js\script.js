document.addEventListener('DOMContentLoaded', function() {
    // Transfer form validation
    const transferForm = document.getElementById('transfer-form');
    if (transferForm) {
        transferForm.addEventListener('submit', function(e) {
            const fromAccount = document.getElementById('from_account').value;
            const toAccount = document.getElementById('to_account').value;
            const amount = parseFloat(document.getElementById('amount').value);
            
            if (fromAccount === toAccount) {
                e.preventDefault();
                alert('You cannot transfer to the same account');
                return false;
            }
            
            if (amount <= 0) {
                e.preventDefault();
                alert('Amount must be greater than zero');
                return false;
            }
            
            return true;
        });
    }
    
    // Deposit form validation
    const depositForm = document.getElementById('deposit-form');
    if (depositForm) {
        depositForm.addEventListener('submit', function(e) {
            const amount = parseFloat(document.getElementById('amount').value);
            
            if (amount <= 0) {
                e.preventDefault();
                alert('Amount must be greater than zero');
                return false;
            }
            
            return true;
        });
    }
    
    // Withdraw form validation
    const withdrawForm = document.getElementById('withdraw-form');
    if (withdrawForm) {
        withdrawForm.addEventListener('submit', function(e) {
            const amount = parseFloat(document.getElementById('amount').value);
            const maxAmount = parseFloat(document.getElementById('amount').getAttribute('max'));
            
            if (amount <= 0) {
                e.preventDefault();
                alert('Amount must be greater than zero');
                return false;
            }
            
            if (amount > maxAmount) {
                e.preventDefault();
                alert('Insufficient funds. Your current balance is $' + maxAmount.toFixed(2));
                return false;
            }
            
            return true;
        });
    }
    
    // Auto-submit filter form when select changes
    const filterForm = document.getElementById('filter-form');
    if (filterForm) {
        const accountSelect = document.getElementById('account');
        const typeSelect = document.getElementById('type');
        
        accountSelect.addEventListener('change', function() {
            filterForm.submit();
        });
        
        typeSelect.addEventListener('change', function() {
            filterForm.submit();
        });
    }
    
    // Success message auto-hide
    const successMessage = document.querySelector('.success-message');
    if (successMessage) {
        setTimeout(function() {
            successMessage.style.opacity = '0';
            setTimeout(function() {
                successMessage.style.display = 'none';
            }, 500);
        }, 5000);
    }
});
