<?php
// Database connection parameters
$host = "localhost";
$username = "root";
$password = "";
$database = "banking_system";

// Create connection
$conn = new mysqli($host, $username, $password);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>Creating Branch Table</h1>";

// Create database if it doesn't exist
$sql = "CREATE DATABASE IF NOT EXISTS `$database`";
if ($conn->query($sql) === TRUE) {
    echo "<p>Database created or already exists.</p>";
} else {
    echo "<p>Error creating database: " . $conn->error . "</p>";
    exit;
}

// Select the database
$conn->select_db($database);

// Create branch table
$sql = "CREATE TABLE IF NOT EXISTS `branch` (
  `BranchID` int(11) NOT NULL AUTO_INCREMENT,
  `BranchName` varchar(100) NOT NULL DEFAULT 'Main Branch',
  `Location` varchar(255) NOT NULL DEFAULT 'Butuan',
  PRIMARY KEY (`BranchID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql) === TRUE) {
    echo "<p>Branch table created successfully.</p>";
} else {
    echo "<p>Error creating branch table: " . $conn->error . "</p>";
}

// Check if branch table is empty
$result = $conn->query("SELECT COUNT(*) as count FROM `branch`");
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    // Insert branch data
    $sql = "INSERT INTO `branch` (`BranchID`, `BranchName`, `Location`) VALUES
    (1, 'Main Branch', 'Butuan'),
    (2, 'Downtown Branch', 'Butuan City Center'),
    (3, 'Sibagat Branch', 'Sibagat')";

    if ($conn->query($sql) === TRUE) {
        echo "<p>Branch data inserted successfully.</p>";
    } else {
        echo "<p>Error inserting branch data: " . $conn->error . "</p>";
    }
} else {
    echo "<p>Branch data already exists.</p>";
}

echo "<p><a href='signup.php'>Go to Signup Page</a></p>";

$conn->close();
?>
