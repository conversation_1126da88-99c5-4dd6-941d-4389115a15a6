<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['customer_id'])) {
    header("Location: index.php");
    exit();
}

require_once 'config/db_connect.php';

// Get customer information
$customer_id = $_SESSION['customer_id'];
$customer_name = $_SESSION['customer_name'];

// Get accounts for this customer for filtering
$accounts_sql = "SELECT AccountID, AccountType FROM account WHERE CustomerID = $customer_id";
$accounts_result = $conn->query($accounts_sql);

// Build accounts array for filtering
$accounts = array();
if ($accounts_result->num_rows > 0) {
    while($row = $accounts_result->fetch_assoc()) {
        $accounts[$row['AccountID']] = $row['AccountType'];
    }
}

// Filter variables
$account_filter = isset($_GET['account']) ? intval($_GET['account']) : 0;
$type_filter = isset($_GET['type']) ? $_GET['type'] : '';

// Build query with filters
$transactions_sql = "SELECT t.TransactionID, t.AccountID, t.Amount, t.Date, t.Type, a.AccountType
                    FROM transaction t
                    JOIN account a ON t.AccountID = a.AccountID
                    WHERE a.CustomerID = $customer_id";

if ($account_filter > 0) {
    $transactions_sql .= " AND t.AccountID = $account_filter";
}

if (!empty($type_filter)) {
    $transactions_sql .= " AND t.Type = '" . $conn->real_escape_string($type_filter) . "'";
}

$transactions_sql .= " ORDER BY t.Date DESC";
$transactions_result = $conn->query($transactions_sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Transactions</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/landing.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Banking System</h1>
            <div class="user-info">
                <p>Welcome, <?php echo htmlspecialchars($customer_name); ?></p>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </header>

        <nav>
            <ul>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="transactions.php" class="active">Transactions</a></li>
                <li><a href="transfer.php">Transfer Funds</a></li>
                <li><a href="account_request.php">Request New Account</a></li>
                <li><a href="profile.php">My Profile</a></li>
            </ul>
        </nav>

        <main>
            <div class="breadcrumb">
                <a href="dashboard.php">Dashboard</a> &gt;
                <span>Transactions</span>
            </div>

            <section class="transactions-section full-width">
                <h2>Transaction History</h2>

                <div class="filters">
                    <form method="get" action="transactions.php" id="filter-form">
                        <div class="filter-group">
                            <label for="account">Account:</label>
                            <select name="account" id="account">
                                <option value="0">All Accounts</option>
                                <?php foreach($accounts as $id => $type): ?>
                                    <option value="<?php echo $id; ?>" <?php echo $account_filter == $id ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($type); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="type">Type:</label>
                            <select name="type" id="type">
                                <option value="">All Types</option>
                                <option value="Credit" <?php echo $type_filter == 'Credit' ? 'selected' : ''; ?>>Credit</option>
                                <option value="Debit" <?php echo $type_filter == 'Debit' ? 'selected' : ''; ?>>Debit</option>
                                <option value="Transfer" <?php echo $type_filter == 'Transfer' ? 'selected' : ''; ?>>Transfer</option>
                            </select>
                        </div>

                        <button type="submit" class="filter-btn">Apply Filters</button>
                    </form>
                </div>

                <?php if ($transactions_result && $transactions_result->num_rows > 0): ?>
                    <table class="transactions-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Account</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($transaction = $transactions_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $transaction['TransactionID']; ?></td>
                                    <td>
                                        <a href="transactions.php?account=<?php echo $transaction['AccountID']; ?>">
                                            <?php echo htmlspecialchars($transaction['AccountType']); ?>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="<?php echo strtolower($transaction['Type']); ?>">
                                            <?php echo htmlspecialchars($transaction['Type']); ?>
                                        </span>
                                    </td>
                                    <td class="<?php echo strtolower($transaction['Type']); ?>">
                                        $<?php echo number_format($transaction['Amount'], 2); ?>
                                    </td>
                                    <td><?php echo date('M d, Y', strtotime($transaction['Date'])); ?></td>
                                    <td>
                                        <a href="transaction_details.php?id=<?php echo $transaction['TransactionID']; ?>" class="btn btn-sm">Details</a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>No transactions found matching your criteria.</p>
                <?php endif; ?>
            </section>
        </main>

        <footer>
            <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
        </footer>
    </div>

    <script src="js/script.js"></script>
</body>
</html>

<?php
$conn->close();
?>
