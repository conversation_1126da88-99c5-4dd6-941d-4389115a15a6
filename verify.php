<?php
session_start();

// Enable development mode for testing (set to false in production)
define('DEVELOPMENT_MODE', true);

// Redirect if already logged in
if (isset($_SESSION['customer_id'])) {
    header("Location: dashboard.php");
    exit();
} elseif (isset($_SESSION['admin_id'])) {
    header("Location: admin/dashboard.php");
    exit();
}

require_once 'config/db_connect.php';
require_once 'includes/token_service.php';
require_once 'includes/email_service.php';

$message = "";
$status = "";
$customer = null;

// Check if verification code is provided
if (isset($_GET['code']) && !empty($_GET['code'])) {
    $code = $_GET['code'];

    // Validate the verification token
    $validation_result = validate_verification_token($conn, $code);
    $status = $validation_result['status'];
    $message = $validation_result['message'];
    $customer = $validation_result['customer'];

    // If token is valid and not already verified, mark as verified
    if ($status === 'success' && $customer) {
        $customer_id = $customer['CustomerID'];
        $email = $customer['Email'];

        if (mark_email_as_verified($conn, $customer_id)) {
            $message = "Your email has been verified successfully! Your account is now pending admin approval.";
            $status = "success";

            // Log verification activity
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
            log_verification_activity('verify', $customer_id, $email, 'success', $ip_address, $user_agent);
        } else {
            $message = "Verification failed: " . $conn->error;
            $status = "error";

            // Log verification activity
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
            log_verification_activity('verify', $customer_id, $email, 'error', $ip_address, $user_agent);
        }
    } else if ($status === 'error' && $customer) {
        // Log verification activity for failed attempts with valid customer
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        log_verification_activity('verify', $customer['CustomerID'], $customer['Email'], 'error', $ip_address, $user_agent);
    }
} else {
    $message = "No verification code provided.";
    $status = "error";
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Email Verification</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/landing.css">
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="css/enhanced-style.css">
    <link rel="stylesheet" href="css/banking-patterns.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Banking System</h1>
            <nav class="top-nav">
                <ul>
                    <li><a href="index.php">Home</a></li>
                    <li><a href="customer_login.php">Customer Login</a></li>
                    <li><a href="admin/login.php">Admin Login</a></li>
                    <li><a href="signup.php">Sign Up</a></li>
                </ul>
            </nav>
        </header>

        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <h2>Email Verification</h2>
                    <p>Verify your email address to access your account</p>
                </div>

                <?php if ($status == "success"): ?>
                    <div class="success-message">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $message; ?>
                    </div>

                    <div class="verification-steps">
                        <div class="step completed">
                            <div class="step-icon"><i class="fas fa-envelope"></i></div>
                            <div class="step-content">
                                <h3>Email Verified</h3>
                                <p>Your email has been successfully verified.</p>
                            </div>
                        </div>

                        <div class="step active">
                            <div class="step-icon"><i class="fas fa-user-check"></i></div>
                            <div class="step-content">
                                <h3>Admin Approval</h3>
                                <p>Your account is now pending admin approval.</p>
                            </div>
                        </div>

                        <div class="step">
                            <div class="step-icon"><i class="fas fa-unlock-alt"></i></div>
                            <div class="step-content">
                                <h3>Account Access</h3>
                                <p>Once approved, you can log in to your account.</p>
                            </div>
                        </div>
                    </div>
                <?php elseif ($status == "error"): ?>
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo $message; ?>
                    </div>

                    <div class="verification-help">
                        <h3>Need Help?</h3>
                        <p>If you're having trouble verifying your email, try these options:</p>
                        <ul>
                            <li>Check if you've clicked the correct link from your email</li>
                            <li>Request a new verification email</li>
                            <li>Contact our support team for assistance</li>
                        </ul>
                    </div>
                <?php else: ?>
                    <div class="info-message">
                        <i class="fas fa-info-circle"></i>
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <div class="action-buttons" style="margin-top: 20px;">
                    <?php if ($status == "success" || $status == "info"): ?>
                        <a href="customer_login.php" class="btn-login">
                            <i class="fas fa-sign-in-alt"></i>
                            Go to Login
                        </a>
                    <?php else: ?>
                        <a href="verify_resend.php" class="btn-login">
                            <i class="fas fa-paper-plane"></i>
                            Resend Verification Email
                        </a>
                    <?php endif; ?>
                </div>

                <div class="login-footer">
                    <p>
                        <?php if ($status == "success" || $status == "info"): ?>
                            Need help? <a href="contact.php" class="signup-link">Contact Support</a>
                        <?php else: ?>
                            Want to try again? <a href="signup.php" class="signup-link">Back to Sign Up</a>
                        <?php endif; ?>
                    </p>
                </div>
            </div>

            <div class="login-info">
                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Why Verify?</h3>
                    <p>Email verification helps protect your account and ensures we can contact you about important account information.</p>
                </div>

                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-envelope-open-text"></i>
                    </div>
                    <h3>Check Your Email</h3>
                    <p>We've sent a verification link to your email address. Click the link to verify your account.</p>
                </div>

                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <h3>Need Help?</h3>
                    <p>If you didn't receive the verification email, check your spam folder or request a new verification email.</p>
                </div>
            </div>
        </div>

        <footer>
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Banking System</h3>
                    <p>Your trusted financial partner since 2025.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="customer_login.php">Customer Login</a></li>
                        <li><a href="admin/login.php">Admin Login</a></li>
                        <li><a href="signup.php">Sign Up</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Us</h3>
                    <p>Email: <EMAIL></p>
                    <p>Phone: (*************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
            </div>
        </footer>
    </div>

    <style>
        .verification-steps {
            margin: 30px 0;
        }

        .step {
            display: flex;
            margin-bottom: 20px;
            opacity: 0.6;
        }

        .step.active, .step.completed {
            opacity: 1;
        }

        .step-icon {
            width: 40px;
            height: 40px;
            background-color: #e8eaf6;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
        }

        .step.active .step-icon {
            background-color: #3f51b5;
            color: white;
        }

        .step.completed .step-icon {
            background-color: #4caf50;
            color: white;
        }

        .step-content h3 {
            margin: 0 0 5px 0;
            font-size: 1.1rem;
            color: #1a237e;
        }

        .step-content p {
            margin: 0;
            color: #757575;
            font-size: 0.9rem;
        }

        .verification-help {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .verification-help h3 {
            color: #1a237e;
            margin-top: 0;
            margin-bottom: 10px;
        }

        .verification-help ul {
            margin: 0;
            padding-left: 20px;
        }

        .verification-help li {
            margin-bottom: 8px;
            color: #555;
        }
    </style>
</body>
</html>
