<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['customer_id'])) {
    header("Location: index.php");
    exit();
}

require_once 'config/db_connect.php';

// Get customer information
$customer_id = $_SESSION['customer_id'];
$customer_name = $_SESSION['customer_name'];

// Check if account ID is provided
if (!isset($_GET['account']) || empty($_GET['account'])) {
    header("Location: dashboard.php");
    exit();
}

$account_id = intval($_GET['account']);

// Verify account belongs to the logged-in customer
$account_sql = "SELECT AccountID, AccountType, Balance FROM account 
                WHERE AccountID = $account_id AND CustomerID = $customer_id";
$account_result = $conn->query($account_sql);

if ($account_result->num_rows != 1) {
    header("Location: dashboard.php");
    exit();
}

$account = $account_result->fetch_assoc();
$message = '';
$error = '';

// Process withdrawal form
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $amount = floatval($_POST['amount']);
    $current_date = date('Y-m-d');
    
    // Validate withdrawal
    if ($amount <= 0) {
        $error = "Amount must be greater than zero";
    } elseif ($amount > $account['Balance']) {
        $error = "Insufficient funds. Your current balance is $" . number_format($account['Balance'], 2);
    } else {
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Update account balance
            $update_account = "UPDATE account SET Balance = Balance - $amount WHERE AccountID = $account_id";
            $conn->query($update_account);
            
            // Record transaction
            $insert_transaction = "INSERT INTO transaction (AccountID, Amount, Date, Type) 
                                  VALUES ($account_id, $amount, '$current_date', 'Debit')";
            $conn->query($insert_transaction);
            
            // Commit transaction
            $conn->commit();
            
            $message = "Withdrawal of $" . number_format($amount, 2) . " completed successfully";
            
            // Update account balance in the current page
            $account['Balance'] -= $amount;
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $error = "Withdrawal failed: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Withdraw</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Banking System</h1>
            <div class="user-info">
                <p>Welcome, <?php echo htmlspecialchars($customer_name); ?></p>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </header>
        
        <nav>
            <ul>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="transactions.php">Transactions</a></li>
                <li><a href="transfer.php">Transfer Funds</a></li>
            </ul>
        </nav>
        
        <main>
            <section class="transaction-form-section">
                <h2>Withdraw Funds</h2>
                
                <div class="account-info">
                    <h3><?php echo htmlspecialchars($account['AccountType']); ?> Account</h3>
                    <p>Account #: <?php echo $account['AccountID']; ?></p>
                    <p>Current Balance: $<?php echo number_format($account['Balance'], 2); ?></p>
                </div>
                
                <?php if (!empty($message)): ?>
                    <div class="success-message"><?php echo $message; ?></div>
                <?php endif; ?>
                
                <?php if (!empty($error)): ?>
                    <div class="error-message"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . '?account=' . $account_id; ?>" id="withdraw-form">
                    <div class="form-group">
                        <label for="amount">Withdrawal Amount:</label>
                        <input type="number" id="amount" name="amount" min="0.01" step="0.01" max="<?php echo $account['Balance']; ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn">Withdraw Funds</button>
                        <a href="dashboard.php" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </section>
        </main>
        
        <footer>
            <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
        </footer>
    </div>
    
    <script src="js/script.js"></script>
</body>
</html>

<?php
$conn->close();
?>
