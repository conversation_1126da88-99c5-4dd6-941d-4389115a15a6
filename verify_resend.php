<?php
session_start();

// Enable development mode for testing (set to false in production)
define('DEVELOPMENT_MODE', true);

// Redirect if already logged in
if (isset($_SESSION['customer_id'])) {
    header("Location: dashboard.php");
    exit();
}

require_once 'config/db_connect.php';
require_once 'includes/token_service.php';
require_once 'includes/email_service.php';

// Process resend verification form
$message = "";
$error = "";
$prefilled_email = "";

// Check if email is provided in the URL
if (isset($_GET['email']) && !empty($_GET['email'])) {
    $prefilled_email = filter_var($_GET['email'], FILTER_SANITIZE_EMAIL);
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);

    // Check if email exists and needs verification
    $stmt = $conn->prepare("SELECT CustomerID, Name, Email, IsVerified FROM customer WHERE Email = ?");
    $stmt->bind_param('s', $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 1) {
        $row = $result->fetch_assoc();
        $customer_id = $row['CustomerID'];
        $name = $row['Name'];

        if ($row['IsVerified']) {
            $message = "Your email is already verified. You can now <a href='customer_login.php'>login</a>.";

            // Log verification activity
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
            log_verification_activity('resend', $customer_id, $email, 'info', $ip_address, $user_agent);
        } else {
            // Generate new verification token
            $verification_token = generate_verification_token();

            // Calculate token expiry (24 hours from now)
            $token_expiry = date('Y-m-d H:i:s', strtotime('+24 hours'));

            // Update verification token and expiry
            $update_stmt = $conn->prepare("UPDATE customer SET VerificationCode = ?, VerificationExpiry = ? WHERE CustomerID = ?");
            $update_stmt->bind_param('ssi', $verification_token, $token_expiry, $customer_id);

            if ($update_stmt->execute()) {
                // Send verification email
                $email_result = send_verification_email($email, $name, $verification_token);

                // Log verification activity
                $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                log_verification_activity('resend', $customer_id, $email, $email_result['status'], $ip_address, $user_agent);

                if ($email_result['status'] === 'success') {
                    $message = "A new verification email has been sent to your email address. Please check your inbox and follow the instructions to verify your account.";

                    // For development/testing purposes only - show verification link
                    if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
                        $base_url = get_base_url();
                        $verification_link = $base_url . 'verify.php?code=' . $verification_token;
                        $message .= "<div class='demo-verification-link'><strong>Demo:</strong> <a href='$verification_link'>Click here to verify your email</a></div>";
                    }
                } else {
                    $error = "Error sending verification email: " . $email_result['message'];
                }

                $update_stmt->close();
            } else {
                $error = "Error updating verification code: " . $conn->error;

                // Log verification activity
                $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                log_verification_activity('resend', $customer_id, $email, 'error', $ip_address, $user_agent);
            }
        }

        $stmt->close();
    } else {
        $error = "Email address not found. Please check your email or <a href='signup.php'>sign up</a> for a new account.";
        $stmt->close();
    }

    $conn->close();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Resend Verification</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/landing.css">
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="css/enhanced-style.css">
    <link rel="stylesheet" href="css/banking-patterns.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Banking System</h1>
            <nav class="top-nav">
                <ul>
                    <li><a href="index.php">Home</a></li>
                    <li><a href="customer_login.php">Customer Login</a></li>
                    <li><a href="admin/login.php">Admin Login</a></li>
                    <li><a href="signup.php">Sign Up</a></li>
                </ul>
            </nav>
        </header>

        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <h2>Resend Verification Email</h2>
                    <p>Enter your email address to receive a new verification link</p>
                </div>

                <?php if (!empty($error)): ?>
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($message)): ?>
                    <div class="success-message">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="login-form">
                    <div class="form-group">
                        <label for="email">
                            <i class="fas fa-envelope"></i>
                            Email Address
                        </label>
                        <input type="email" id="email" name="email" placeholder="Enter your email" value="<?php echo htmlspecialchars($prefilled_email); ?>" required>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn-login">
                            <i class="fas fa-paper-plane"></i>
                            Resend Verification
                        </button>
                    </div>
                </form>

                <div class="login-footer">
                    <p>Remember your login details? <a href="customer_login.php" class="signup-link">Back to Login</a></p>
                </div>
            </div>

            <div class="login-info">
                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-envelope-open-text"></i>
                    </div>
                    <h3>Email Verification</h3>
                    <p>We require email verification to ensure the security of your account and protect your personal information.</p>
                </div>

                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <h3>Check Your Inbox</h3>
                    <p>After submitting your email, please check your inbox (and spam folder) for the verification link.</p>
                </div>

                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <h3>Need Help?</h3>
                    <p>If you're having trouble with verification, please contact our customer support team for assistance.</p>
                </div>
            </div>
        </div>

        <footer>
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Banking System</h3>
                    <p>Your trusted financial partner since 2025.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="customer_login.php">Customer Login</a></li>
                        <li><a href="admin/login.php">Admin Login</a></li>
                        <li><a href="signup.php">Sign Up</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Us</h3>
                    <p>Email: <EMAIL></p>
                    <p>Phone: (*************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
            </div>
        </footer>
    </div>

    <style>
        .demo-verification-link {
            margin-top: 15px;
            padding: 10px;
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            border-radius: 4px;
            color: #856404;
        }

        .demo-verification-link a {
            color: #533f03;
            font-weight: bold;
            text-decoration: underline;
        }
    </style>
</body>
</html>
