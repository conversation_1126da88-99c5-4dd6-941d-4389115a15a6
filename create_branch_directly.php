<?php
// Output all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database connection parameters
$host = "localhost";
$username = "root";
$password = "";

echo "<h1>Creating Branch Table Directly</h1>";

// Connect to MySQL server
$conn = new mysqli($host, $username, $password);

// Check connection
if ($conn->connect_error) {
    die("<p>Connection to MySQL server failed: " . $conn->connect_error . "</p>");
} else {
    echo "<p>Successfully connected to MySQL server.</p>";
}

// Create database if it doesn't exist
if (!$conn->query("CREATE DATABASE IF NOT EXISTS banking_system")) {
    echo "<p>Error creating database: " . $conn->error . "</p>";
}

// Select the database
if (!$conn->select_db("banking_system")) {
    die("<p>Error selecting database: " . $conn->error . "</p>");
} else {
    echo "<p>Successfully selected 'banking_system' database.</p>";
}

// Drop branch table if it exists (to ensure a clean start)
if ($conn->query("DROP TABLE IF EXISTS branch")) {
    echo "<p>Dropped existing branch table (if any).</p>";
} else {
    echo "<p>Error dropping branch table: " . $conn->error . "</p>";
}

// Create branch table
$sql = "CREATE TABLE branch (
  BranchID int(11) NOT NULL AUTO_INCREMENT,
  BranchName varchar(100) NOT NULL DEFAULT 'Main Branch',
  Location varchar(255) NOT NULL DEFAULT 'Butuan',
  PRIMARY KEY (BranchID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql)) {
    echo "<p>Branch table created successfully.</p>";
} else {
    echo "<p>Error creating branch table: " . $conn->error . "</p>";
}

// Insert branch data
$sql = "INSERT INTO branch (BranchID, BranchName, Location) VALUES
(1, 'Main Branch', 'Butuan'),
(2, 'Downtown Branch', 'Butuan City Center'),
(3, 'Sibagat Branch', 'Sibagat')";

if ($conn->query($sql)) {
    echo "<p>Branch data inserted successfully.</p>";
} else {
    echo "<p>Error inserting branch data: " . $conn->error . "</p>";
}

// Verify branch table exists and has data
$result = $conn->query("SELECT * FROM branch");
if ($result) {
    echo "<p>Branch table verification:</p>";
    echo "<table border='1'>";
    echo "<tr><th>BranchID</th><th>BranchName</th><th>Location</th></tr>";
    
    if ($result->num_rows > 0) {
        while($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row["BranchID"] . "</td>";
            echo "<td>" . $row["BranchName"] . "</td>";
            echo "<td>" . $row["Location"] . "</td>";
            echo "</tr>";
        }
    } else {
        echo "<tr><td colspan='3'>No data in branch table.</td></tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>Error verifying branch table: " . $conn->error . "</p>";
}

// Close connection
$conn->close();

echo "<p><a href='signup.php'>Try Signup Page Again</a></p>";
?>
