/* Admin Styles */

/* Admin Header */
.admin-header {
    background-color: #1a237e;
    color: white;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-header h1 {
    color: white;
    margin-bottom: 0;
}

.admin-header .user-info {
    display: flex;
    align-items: center;
}

.admin-header .user-info p {
    margin-right: 15px;
    margin-bottom: 0;
    color: white;
}

/* Admin Navigation */
.admin-nav {
    background-color: #3949ab;
    margin-bottom: 0;
}

.admin-nav ul {
    display: flex;
    list-style: none;
    background-color: transparent;
    border-radius: 0;
    padding: 0;
    margin: 0;
}

.admin-nav li {
    flex: 1;
    text-align: center;
}

.admin-nav a {
    display: block;
    color: white;
    padding: 15px;
    transition: background-color 0.3s;
}

.admin-nav a:hover {
    background-color: #303f9f;
    text-decoration: none;
}

.admin-nav a.active {
    background-color: #283593;
    font-weight: bold;
}

/* Admin Main Content */
.admin-main {
    background-color: white;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 30px;
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-card h3 {
    color: #1a237e;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #1a237e;
    margin-bottom: 10px;
}

.stat-card p {
    color: #757575;
    margin-bottom: 15px;
}

/* Admin Tables */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.admin-table th, .admin-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.admin-table th {
    background-color: #f5f5f5;
    font-weight: bold;
    color: #1a237e;
}

.admin-table tr:hover {
    background-color: #f9f9f9;
}

.admin-table .actions {
    display: flex;
    gap: 5px;
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-active {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.status-pending {
    background-color: #fff8e1;
    color: #ff8f00;
}

.status-suspended {
    background-color: #ffebee;
    color: #c62828;
}

/* Transaction Types */
.credit {
    color: #4caf50;
    font-weight: bold;
}

.debit {
    color: #f44336;
    font-weight: bold;
}

.transfer {
    color: #2196f3;
    font-weight: bold;
}

/* Filters */
.filters {
    background-color: #f5f5f5;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.filter-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

/* Info Box */
.info-box {
    background-color: #e8eaf6;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 5px solid #3f51b5;
}

.info-box h3 {
    color: #1a237e;
    margin-bottom: 15px;
}

.info-box p {
    margin-bottom: 10px;
}

/* View Tabs */
.view-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #ddd;
}

.tab {
    padding: 10px 20px;
    color: #757575;
    text-decoration: none;
    border-bottom: 3px solid transparent;
    transition: all 0.3s;
}

.tab:hover {
    color: #1a237e;
    text-decoration: none;
}

.tab.active {
    color: #1a237e;
    border-bottom-color: #1a237e;
    font-weight: bold;
}

/* Quick Actions */
.quick-actions {
    margin-top: 30px;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

/* Admin Login */
.admin-login {
    max-width: 500px;
    margin: 50px auto;
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.admin-login h2 {
    color: #1a237e;
    margin-bottom: 30px;
    text-align: center;
}

/* Small Buttons */
.btn-sm {
    padding: 5px 10px;
    font-size: 0.9rem;
}

.btn-success {
    background-color: #4caf50;
}

.btn-success:hover {
    background-color: #388e3c;
}

.btn-danger {
    background-color: #f44336;
}

.btn-danger:hover {
    background-color: #d32f2f;
}

/* Info Message */
.info-message {
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    background-color: #e3f2fd;
    color: #0d47a1;
    border-left: 5px solid #2196f3;
}

/* Dashboard Charts */
.dashboard-charts {
    margin-bottom: 30px;
}

.chart-filters {
    background-color: #f5f5f5;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.chart-filters h3 {
    color: #1a237e;
    margin: 0;
    margin-right: 20px;
    margin-bottom: 10px;
}

.filter-controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.charts-container {
    margin-top: 20px;
}

.chart-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 20px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.chart-header h4 {
    color: #1a237e;
    margin: 0;
    font-size: 1.1rem;
}

.export-btn {
    background-color: #4caf50;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.3s;
}

.export-btn:hover {
    background-color: #388e3c;
}

.export-btn i {
    font-size: 1rem;
}

.chart-wrapper {
    position: relative;
    height: 300px;
    width: 100%;
}

.chart-row {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.half-width {
    flex: 1 1 calc(50% - 10px);
    min-width: 300px;
}

/* Responsive Design */
@media (max-width: 992px) {
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .chart-filters {
        flex-direction: column;
        align-items: flex-start;
    }

    .filter-controls {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .admin-header {
        flex-direction: column;
        text-align: center;
        padding: 15px;
    }

    .admin-header .user-info {
        margin-top: 15px;
        flex-direction: column;
    }

    .admin-header .user-info p {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .admin-nav ul {
        flex-direction: column;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .admin-table {
        display: block;
        overflow-x: auto;
    }

    .filter-row {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
    }

    .chart-row {
        flex-direction: column;
    }

    .half-width {
        width: 100%;
    }
}
