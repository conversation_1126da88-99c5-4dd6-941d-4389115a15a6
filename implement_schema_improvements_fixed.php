<?php
/**
 * Fixed Implementation of Schema Improvements for Admin Relationships
 * Compatible with MySQL/MariaDB versions that don't support IF NOT EXISTS for columns
 */

require_once 'config/db_connect.php';

echo "<h1>Implementing Banking System Schema Improvements (Fixed)</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .step { background-color: #f0f8ff; padding: 10px; margin: 10px 0; border-left: 4px solid #0066cc; }
</style>";

echo "<p class='info'>Implementing admin relationship improvements for banking_system_new</p>";

$errors = [];
$successes = [];

// Helper function to check if column exists
function columnExists($conn, $table, $column) {
    $result = $conn->query("SHOW COLUMNS FROM `$table` LIKE '$column'");
    return $result->num_rows > 0;
}

// Helper function to check if table exists
function tableExists($conn, $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    return $result->num_rows > 0;
}

// Step 1: Create admin_roles table
echo "<div class='step'>";
echo "<h2>Step 1: Creating Admin Roles System</h2>";

if (!tableExists($conn, 'admin_roles')) {
    $sql = "CREATE TABLE admin_roles (
        RoleID INT(11) AUTO_INCREMENT PRIMARY KEY,
        RoleName VARCHAR(50) NOT NULL UNIQUE,
        Description TEXT,
        Permissions JSON,
        CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    if ($conn->query($sql)) {
        echo "<p class='success'>✅ Admin roles table created successfully</p>";
        $successes[] = "Admin roles table created";
        
        // Insert default roles
        $roles_sql = "INSERT INTO admin_roles (RoleID, RoleName, Description, Permissions) VALUES
        (1, 'Super Admin', 'Full system access', '{\"all\": true}'),
        (2, 'Manager', 'Account and transaction management', '{\"accounts\": true, \"transactions\": true, \"reports\": true}'),
        (3, 'Teller', 'Basic transaction processing', '{\"transactions\": true, \"customer_view\": true}'),
        (4, 'Auditor', 'Read-only access for auditing', '{\"view_all\": true, \"reports\": true}')";
        
        if ($conn->query($roles_sql)) {
            echo "<p class='success'>✅ Default admin roles inserted</p>";
        } else {
            echo "<p class='error'>❌ Error inserting default roles: " . $conn->error . "</p>";
            $errors[] = "Failed to insert default roles";
        }
    } else {
        echo "<p class='error'>❌ Error creating admin roles table: " . $conn->error . "</p>";
        $errors[] = "Failed to create admin_roles table";
    }
} else {
    echo "<p class='warning'>⚠️ Admin roles table already exists</p>";
}
echo "</div>";

// Step 2: Add RoleID to admin table
echo "<div class='step'>";
echo "<h2>Step 2: Adding Role Support to Admin Table</h2>";

if (!columnExists($conn, 'admin', 'RoleID')) {
    $sql = "ALTER TABLE admin ADD COLUMN RoleID INT(11) DEFAULT 1";
    if ($conn->query($sql)) {
        echo "<p class='success'>✅ RoleID column added to admin table</p>";
        $successes[] = "RoleID column added to admin";
    } else {
        echo "<p class='error'>❌ Error adding RoleID column: " . $conn->error . "</p>";
        $errors[] = "Failed to add RoleID column";
    }
} else {
    echo "<p class='warning'>⚠️ RoleID column already exists in admin table</p>";
}

// Add foreign key constraint (only if both table and column exist)
if (tableExists($conn, 'admin_roles') && columnExists($conn, 'admin', 'RoleID')) {
    $sql = "ALTER TABLE admin ADD CONSTRAINT fk_admin_role FOREIGN KEY (RoleID) REFERENCES admin_roles(RoleID)";
    if ($conn->query($sql)) {
        echo "<p class='success'>✅ Foreign key constraint added for admin roles</p>";
        $successes[] = "Admin role foreign key added";
    } else {
        echo "<p class='warning'>⚠️ Foreign key constraint may already exist: " . $conn->error . "</p>";
    }
}
echo "</div>";

// Step 3: Create admin_actions audit table
echo "<div class='step'>";
echo "<h2>Step 3: Creating Admin Actions Audit Log</h2>";

if (!tableExists($conn, 'admin_actions')) {
    $sql = "CREATE TABLE admin_actions (
        ActionID INT(11) AUTO_INCREMENT PRIMARY KEY,
        AdminID INT(11) NOT NULL,
        ActionType ENUM('CREATE', 'UPDATE', 'DELETE', 'APPROVE', 'REJECT', 'LOGIN', 'LOGOUT', 'VIEW') NOT NULL,
        TableName VARCHAR(50),
        RecordID INT(11),
        Description TEXT,
        OldValues JSON,
        NewValues JSON,
        ActionDate DATETIME DEFAULT CURRENT_TIMESTAMP,
        IPAddress VARCHAR(45),
        UserAgent TEXT,
        FOREIGN KEY (AdminID) REFERENCES admin(AdminID) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    if ($conn->query($sql)) {
        echo "<p class='success'>✅ Admin actions audit table created successfully</p>";
        $successes[] = "Admin actions audit table created";
    } else {
        echo "<p class='error'>❌ Error creating admin actions table: " . $conn->error . "</p>";
        $errors[] = "Failed to create admin_actions table";
    }
} else {
    echo "<p class='warning'>⚠️ Admin actions table already exists</p>";
}
echo "</div>";

// Step 4: Enhance pending_account table
echo "<div class='step'>";
echo "<h2>Step 4: Adding Admin Approval to Pending Accounts</h2>";

$pending_columns = [
    "ApprovedBy INT(11) NULL",
    "ApprovalDate DATETIME NULL", 
    "ApprovalNotes TEXT NULL",
    "RejectedBy INT(11) NULL",
    "RejectionDate DATETIME NULL",
    "RejectionReason TEXT NULL"
];

foreach ($pending_columns as $column_def) {
    $column_name = explode(' ', $column_def)[0];
    if (!columnExists($conn, 'pending_account', $column_name)) {
        $sql = "ALTER TABLE pending_account ADD COLUMN $column_def";
        if ($conn->query($sql)) {
            echo "<p class='success'>✅ Added column: $column_name</p>";
        } else {
            echo "<p class='error'>❌ Error adding column $column_name: " . $conn->error . "</p>";
        }
    } else {
        echo "<p class='warning'>⚠️ Column $column_name already exists</p>";
    }
}

// Add foreign key constraints for pending_account
$pending_fk_constraints = [
    "ALTER TABLE pending_account ADD CONSTRAINT fk_pending_approved_by FOREIGN KEY (ApprovedBy) REFERENCES admin(AdminID)",
    "ALTER TABLE pending_account ADD CONSTRAINT fk_pending_rejected_by FOREIGN KEY (RejectedBy) REFERENCES admin(AdminID)"
];

foreach ($pending_fk_constraints as $constraint) {
    if ($conn->query($constraint)) {
        echo "<p class='success'>✅ Foreign key constraint added for pending account approvals</p>";
    } else {
        echo "<p class='warning'>⚠️ Foreign key constraint may already exist: " . $conn->error . "</p>";
    }
}
echo "</div>";

// Step 5: Enhance account table
echo "<div class='step'>";
echo "<h2>Step 5: Adding Admin Tracking to Accounts</h2>";

$account_columns = [
    "CreatedBy INT(11) NULL",
    "CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP",
    "LastModifiedBy INT(11) NULL", 
    "LastModifiedDate DATETIME NULL",
    "Status ENUM('active', 'suspended', 'closed') DEFAULT 'active'"
];

foreach ($account_columns as $column_def) {
    $column_name = explode(' ', $column_def)[0];
    if (!columnExists($conn, 'account', $column_name)) {
        $sql = "ALTER TABLE account ADD COLUMN $column_def";
        if ($conn->query($sql)) {
            echo "<p class='success'>✅ Added column: $column_name</p>";
        } else {
            echo "<p class='error'>❌ Error adding column $column_name: " . $conn->error . "</p>";
        }
    } else {
        echo "<p class='warning'>⚠️ Column $column_name already exists</p>";
    }
}

// Add foreign key constraints for account
$account_fk_constraints = [
    "ALTER TABLE account ADD CONSTRAINT fk_account_created_by FOREIGN KEY (CreatedBy) REFERENCES admin(AdminID)",
    "ALTER TABLE account ADD CONSTRAINT fk_account_modified_by FOREIGN KEY (LastModifiedBy) REFERENCES admin(AdminID)"
];

foreach ($account_fk_constraints as $constraint) {
    if ($conn->query($constraint)) {
        echo "<p class='success'>✅ Foreign key constraint added for account admin tracking</p>";
    } else {
        echo "<p class='warning'>⚠️ Foreign key constraint may already exist: " . $conn->error . "</p>";
    }
}
echo "</div>";

// Step 6: Enhance transaction table
echo "<div class='step'>";
echo "<h2>Step 6: Adding Admin Oversight to Transactions</h2>";

$transaction_columns = [
    "ProcessedBy INT(11) NULL",
    "RequiresApproval BOOLEAN DEFAULT FALSE",
    "ApprovedBy INT(11) NULL",
    "ApprovalDate DATETIME NULL",
    "TransactionStatus ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'completed'"
];

foreach ($transaction_columns as $column_def) {
    $column_name = explode(' ', $column_def)[0];
    if (!columnExists($conn, 'transaction', $column_name)) {
        $sql = "ALTER TABLE transaction ADD COLUMN $column_def";
        if ($conn->query($sql)) {
            echo "<p class='success'>✅ Added column: $column_name</p>";
        } else {
            echo "<p class='error'>❌ Error adding column $column_name: " . $conn->error . "</p>";
        }
    } else {
        echo "<p class='warning'>⚠️ Column $column_name already exists</p>";
    }
}

// Add foreign key constraints for transaction
$transaction_fk_constraints = [
    "ALTER TABLE transaction ADD CONSTRAINT fk_transaction_processed_by FOREIGN KEY (ProcessedBy) REFERENCES admin(AdminID)",
    "ALTER TABLE transaction ADD CONSTRAINT fk_transaction_approved_by FOREIGN KEY (ApprovedBy) REFERENCES admin(AdminID)"
];

foreach ($transaction_fk_constraints as $constraint) {
    if ($conn->query($constraint)) {
        echo "<p class='success'>✅ Foreign key constraint added for transaction admin oversight</p>";
    } else {
        echo "<p class='warning'>⚠️ Foreign key constraint may already exist: " . $conn->error . "</p>";
    }
}
echo "</div>";

// Summary
echo "<h2>Implementation Summary</h2>";
echo "<div class='success'>";
echo "<h3>✅ Successes (" . count($successes) . "):</h3>";
echo "<ul>";
foreach ($successes as $success) {
    echo "<li>$success</li>";
}
echo "</ul>";
echo "</div>";

if (!empty($errors)) {
    echo "<div class='error'>";
    echo "<h3>❌ Errors (" . count($errors) . "):</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li><a href='verify_schema_improvements.php'>Verify Schema Improvements</a></li>";
echo "<li>Go to phpMyAdmin and refresh the Designer view to see new relationships</li>";
echo "<li><a href='admin/login.php'>Test Admin Login with New Features</a></li>";
echo "<li><a href='index.php'>Return to Banking System</a></li>";
echo "</ol>";

$conn->close();
?>
