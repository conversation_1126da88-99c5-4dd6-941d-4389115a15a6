/* Dashboard Styles */

/* Dashboard Overview */
.dashboard-overview {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.welcome-card {
    grid-column: 1 / -1;
    background-color: #e8eaf6;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border-left: 5px solid #3f51b5;
}

.welcome-card h2 {
    color: #1a237e;
    margin-bottom: 10px;
    font-size: 1.8rem;
}

.welcome-card p {
    color: #555;
    font-size: 1.1rem;
    margin-bottom: 0;
}

.summary-card {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.summary-card h3 {
    color: #1a237e;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.summary-value {
    font-size: 2rem;
    font-weight: bold;
    color: #1a237e;
    margin-bottom: 10px;
}

.summary-label {
    color: #757575;
    font-size: 0.9rem;
}

/* Quick Actions */
.quick-actions {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.quick-actions h2 {
    color: #1a237e;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.action-card {
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.action-icon {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #3f51b5;
}

.action-card h3 {
    font-size: 1rem;
    margin-bottom: 5px;
    color: #1a237e;
}

.action-card p {
    font-size: 0.8rem;
    color: #757575;
    margin-bottom: 10px;
}

/* Accounts Section */
.accounts-section {
    margin-bottom: 30px;
}

.accounts-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.account-card {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.account-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.account-card h3 {
    color: #1a237e;
    margin-bottom: 15px;
    font-size: 1.3rem;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 10px;
}

.account-number, .branch {
    color: #757575;
    margin-bottom: 10px;
}

.balance {
    font-size: 1.5rem;
    font-weight: bold;
    color: #1a237e;
    margin: 15px 0;
}

.account-status {
    margin-bottom: 15px;
}

.account-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.account-actions .btn {
    flex: 1;
    text-align: center;
}

/* Transactions Section */
.transactions-section {
    margin-bottom: 30px;
}

.transactions-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.transactions-table th, .transactions-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.transactions-table th {
    background-color: #f5f5f5;
    color: #1a237e;
    font-weight: bold;
}

.transactions-table tr:hover {
    background-color: #f9f9f9;
}

.debit {
    color: #f44336;
}

.credit {
    color: #4caf50;
}

.transfer {
    color: #2196f3;
}

.view-all {
    display: inline-block;
    margin-top: 15px;
    color: #3f51b5;
    text-decoration: none;
}

.view-all:hover {
    text-decoration: underline;
}

/* Transaction Details */
.breadcrumb {
    margin-bottom: 20px;
    color: #757575;
}

.breadcrumb a {
    color: #3f51b5;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.transaction-details {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.transaction-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.transaction-header h3 {
    color: #1a237e;
    margin: 0;
}

.transaction-type {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
}

.transaction-amount {
    text-align: center;
    margin-bottom: 30px;
}

.transaction-amount h4 {
    color: #757575;
    margin-bottom: 10px;
}

.amount {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.transaction-date {
    color: #757575;
    font-size: 0.9rem;
}

.transaction-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.info-section {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
}

.info-section h4 {
    color: #1a237e;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
}

.info-item {
    display: flex;
    justify-content: space-between;
}

.info-item label {
    color: #757575;
    font-weight: bold;
}

.action-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* Responsive Design */
@media (max-width: 992px) {
    .dashboard-overview {
        grid-template-columns: 1fr;
    }

    .transaction-info {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .accounts-container {
        grid-template-columns: 1fr;
    }

    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .transactions-table {
        display: block;
        overflow-x: auto;
    }

    .info-item {
        flex-direction: column;
    }

    .info-item label {
        margin-bottom: 5px;
    }
}
