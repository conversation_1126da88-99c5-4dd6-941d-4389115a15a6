<?php
// Output all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Fix Database Connection</h1>";

// Original database connection parameters
$host = "localhost";
$username = "root";
$password = "password1234";
$database = "banking_system_new";

echo "<p>Original connection parameters: Host=$host, Username=$username, Database=$database</p>";

// Try to connect to MySQL server
$conn = new mysqli($host, $username, $password);

// Check connection
if ($conn->connect_error) {
    die("<p>Connection to MySQL server failed: " . $conn->connect_error . "</p>");
} else {
    echo "<p>Successfully connected to MySQL server.</p>";
}

// Check if banking_system_new database exists
$result = $conn->query("SHOW DATABASES LIKE 'banking_system_new'");
if ($result->num_rows > 0) {
    echo "<p>The 'banking_system_new' database exists.</p>";
} else {
    echo "<p>The 'banking_system_new' database does not exist. Creating it now...</p>";

    // Create the database
    if ($conn->query("CREATE DATABASE banking_system_new")) {
        echo "<p>Created 'banking_system_new' database.</p>";
    } else {
        echo "<p>Failed to create 'banking_system_new' database: " . $conn->error . "</p>";
        exit;
    }
}

// Select the database
if (!$conn->select_db("banking_system_new")) {
    die("<p>Error selecting database: " . $conn->error . "</p>");
} else {
    echo "<p>Successfully selected 'banking_system_new' database.</p>";
}

// Check if branch table exists
$result = $conn->query("SHOW TABLES LIKE 'branch'");
if ($result->num_rows > 0) {
    echo "<p>Branch table exists.</p>";
} else {
    echo "<p>Branch table does not exist. Creating it now...</p>";

    // Create branch table
    $sql = "CREATE TABLE branch (
      BranchID int(11) NOT NULL AUTO_INCREMENT,
      BranchName varchar(100) NOT NULL DEFAULT 'Main Branch',
      Location varchar(255) NOT NULL DEFAULT 'Butuan',
      PRIMARY KEY (BranchID)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    if ($conn->query($sql)) {
        echo "<p>Branch table created successfully.</p>";
    } else {
        echo "<p>Error creating branch table: " . $conn->error . "</p>";
    }
}

// Check if branch table has data
$result = $conn->query("SELECT COUNT(*) as count FROM branch");
if (!$result) {
    echo "<p>Error checking branch data: " . $conn->error . "</p>";
} else {
    $row = $result->fetch_assoc();
    if ($row['count'] == 0) {
        echo "<p>Branch table is empty. Inserting data...</p>";

        // Insert branch data
        $sql = "INSERT INTO branch (BranchID, BranchName, Location) VALUES
        (1, 'Main Branch', 'Butuan'),
        (2, 'Downtown Branch', 'Butuan City Center'),
        (3, 'Sibagat Branch', 'Sibagat')";

        if ($conn->query($sql)) {
            echo "<p>Branch data inserted successfully.</p>";
        } else {
            echo "<p>Error inserting branch data: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Branch table already has " . $row['count'] . " records.</p>";
    }
}

// Check if customer table exists
$result = $conn->query("SHOW TABLES LIKE 'customer'");
if ($result->num_rows > 0) {
    echo "<p>Customer table exists.</p>";
} else {
    echo "<p>Customer table does not exist. Creating it now...</p>";

    // Create customer table
    $sql = "CREATE TABLE customer (
      CustomerID int(11) NOT NULL AUTO_INCREMENT,
      Name varchar(100) NOT NULL DEFAULT 'Wendelyn Ferrer',
      Phone varchar(15) NOT NULL DEFAULT '09009090909999',
      Email varchar(100) NOT NULL DEFAULT '<EMAIL>',
      Address varchar(255) NOT NULL DEFAULT 'Sibagat',
      Status enum('pending', 'active', 'suspended') NOT NULL DEFAULT 'pending',
      VerificationCode varchar(32) DEFAULT NULL,
      IsVerified tinyint(1) NOT NULL DEFAULT 0,
      RegistrationDate datetime DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (CustomerID)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    if ($conn->query($sql)) {
        echo "<p>Customer table created successfully.</p>";
    } else {
        echo "<p>Error creating customer table: " . $conn->error . "</p>";
    }
}

// Check if pending_account table exists
$result = $conn->query("SHOW TABLES LIKE 'pending_account'");
if ($result->num_rows > 0) {
    echo "<p>Pending Account table exists.</p>";
} else {
    echo "<p>Pending Account table does not exist. Creating it now...</p>";

    // Create pending_account table
    $sql = "CREATE TABLE pending_account (
      RequestID int(11) NOT NULL AUTO_INCREMENT,
      CustomerID int(11) NOT NULL,
      AccountType varchar(50) NOT NULL DEFAULT 'Savings',
      BranchID int(11) NOT NULL,
      InitialDeposit decimal(15,2) NOT NULL DEFAULT 0.00,
      RequestDate datetime DEFAULT CURRENT_TIMESTAMP,
      Status enum('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
      PRIMARY KEY (RequestID),
      KEY CustomerID (CustomerID),
      KEY BranchID (BranchID)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    if ($conn->query($sql)) {
        echo "<p>Pending Account table created successfully.</p>";
    } else {
        echo "<p>Error creating pending account table: " . $conn->error . "</p>";
    }
}

// Update the db_connect.php file
echo "<h2>Updating Database Connection File</h2>";

$db_connect_file = 'config/db_connect.php';
$db_connect_content = <<<EOT
<?php
// Database connection parameters
\$host = "localhost";
\$username = "root";
\$password = "password1234";
\$database = "banking_system_new";

// Create connection
\$conn = new mysqli(\$host, \$username, \$password, \$database);

// Check connection
if (\$conn->connect_error) {
    die("Connection failed: " . \$conn->connect_error);
}

// Set character set
\$conn->set_charset("utf8mb4");
?>
EOT;

if (file_put_contents($db_connect_file, $db_connect_content)) {
    echo "<p>Database connection file updated successfully.</p>";
} else {
    echo "<p>Error updating database connection file.</p>";
}

// Close connection
$conn->close();

echo "<p><a href='signup.php'>Try Signup Page Again</a></p>";
echo "<p><a href='index.php'>Go to Homepage</a></p>";
?>
