<?php
// Database connection parameters
$host = "localhost";
$username = "root";
$password = "";
$database = "banking_system";

// Create connection
$conn = new mysqli($host, $username, $password);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>Setting up Banking System Tables</h1>";

// Create database if it doesn't exist
$sql = "CREATE DATABASE IF NOT EXISTS `$database`";
if ($conn->query($sql) === TRUE) {
    echo "<p>Database created or already exists.</p>";
} else {
    echo "<p>Error creating database: " . $conn->error . "</p>";
    exit;
}

// Select the database
$conn->select_db($database);

// Create branch table
$sql = "CREATE TABLE IF NOT EXISTS `branch` (
  `BranchID` int(11) NOT NULL AUTO_INCREMENT,
  `BranchName` varchar(100) NOT NULL DEFAULT 'Main Branch',
  `Location` varchar(255) NOT NULL DEFAULT 'Butuan',
  PRIMARY KEY (`BranchID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql) === TRUE) {
    echo "<p>Branch table created successfully.</p>";
} else {
    echo "<p>Error creating branch table: " . $conn->error . "</p>";
}

// Create customer table
$sql = "CREATE TABLE IF NOT EXISTS `customer` (
  `CustomerID` int(11) NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) NOT NULL DEFAULT 'Wendelyn Ferrer',
  `Phone` varchar(15) NOT NULL DEFAULT '09009090909999',
  `Email` varchar(100) NOT NULL DEFAULT '<EMAIL>',
  `Address` varchar(255) NOT NULL DEFAULT 'Sibagat',
  `Status` enum('pending', 'active', 'suspended') NOT NULL DEFAULT 'pending',
  `VerificationCode` varchar(32) DEFAULT NULL,
  `IsVerified` tinyint(1) NOT NULL DEFAULT 0,
  `RegistrationDate` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`CustomerID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql) === TRUE) {
    echo "<p>Customer table created successfully.</p>";
} else {
    echo "<p>Error creating customer table: " . $conn->error . "</p>";
}

// Create account table
$sql = "CREATE TABLE IF NOT EXISTS `account` (
  `AccountID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomerID` int(11) NOT NULL,
  `BranchID` int(11) NOT NULL,
  `AccountType` varchar(50) NOT NULL DEFAULT 'Savings',
  `Balance` decimal(15,2) NOT NULL DEFAULT 0.00,
  PRIMARY KEY (`AccountID`),
  KEY `CustomerID` (`CustomerID`),
  KEY `BranchID` (`BranchID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql) === TRUE) {
    echo "<p>Account table created successfully.</p>";
} else {
    echo "<p>Error creating account table: " . $conn->error . "</p>";
}

// Create transaction table
$sql = "CREATE TABLE IF NOT EXISTS `transaction` (
  `TransactionID` int(11) NOT NULL AUTO_INCREMENT,
  `AccountID` int(11) NOT NULL,
  `Amount` decimal(15,2) NOT NULL DEFAULT 5.01,
  `Date` date DEFAULT NULL,
  `Type` varchar(50) NOT NULL DEFAULT 'Credit',
  PRIMARY KEY (`TransactionID`),
  KEY `AccountID` (`AccountID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql) === TRUE) {
    echo "<p>Transaction table created successfully.</p>";
} else {
    echo "<p>Error creating transaction table: " . $conn->error . "</p>";
}

// Create admin table
$sql = "CREATE TABLE IF NOT EXISTS `admin` (
  `AdminID` int(11) NOT NULL AUTO_INCREMENT,
  `Username` varchar(50) NOT NULL,
  `Password` varchar(255) NOT NULL,
  `Name` varchar(100) NOT NULL,
  `Email` varchar(100) NOT NULL,
  `LastLogin` datetime DEFAULT NULL,
  PRIMARY KEY (`AdminID`),
  UNIQUE KEY `Username` (`Username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql) === TRUE) {
    echo "<p>Admin table created successfully.</p>";
} else {
    echo "<p>Error creating admin table: " . $conn->error . "</p>";
}

// Create pending_account table
$sql = "CREATE TABLE IF NOT EXISTS `pending_account` (
  `RequestID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomerID` int(11) NOT NULL,
  `AccountType` varchar(50) NOT NULL DEFAULT 'Savings',
  `BranchID` int(11) NOT NULL,
  `InitialDeposit` decimal(15,2) NOT NULL DEFAULT 0.00,
  `RequestDate` datetime DEFAULT CURRENT_TIMESTAMP,
  `Status` enum('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
  PRIMARY KEY (`RequestID`),
  KEY `CustomerID` (`CustomerID`),
  KEY `BranchID` (`BranchID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql) === TRUE) {
    echo "<p>Pending Account table created successfully.</p>";
} else {
    echo "<p>Error creating pending account table: " . $conn->error . "</p>";
}

// Insert sample data
echo "<h2>Inserting Sample Data</h2>";

// Check if branch table is empty
$result = $conn->query("SELECT COUNT(*) as count FROM `branch`");
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    // Insert branch data
    $sql = "INSERT INTO `branch` (`BranchID`, `BranchName`, `Location`) VALUES
    (1, 'Main Branch', 'Butuan'),
    (2, 'Downtown Branch', 'Butuan City Center'),
    (3, 'Sibagat Branch', 'Sibagat')";

    if ($conn->query($sql) === TRUE) {
        echo "<p>Branch data inserted successfully.</p>";
    } else {
        echo "<p>Error inserting branch data: " . $conn->error . "</p>";
    }
} else {
    echo "<p>Branch data already exists.</p>";
}

// Check if customer table is empty
$result = $conn->query("SELECT COUNT(*) as count FROM `customer`");
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    // Insert customer data
    $sql = "INSERT INTO `customer` (`CustomerID`, `Name`, `Phone`, `Email`, `Address`, `Status`, `IsVerified`) VALUES
    (1, 'Wendelyn Ferrer', '09009090909999', '<EMAIL>', 'Sibagat', 'active', 1),
    (2, 'John Smith', '09123456789', '<EMAIL>', 'Butuan City', 'active', 1),
    (3, 'Maria Garcia', '09987654321', '<EMAIL>', 'Ampayon', 'active', 1)";

    if ($conn->query($sql) === TRUE) {
        echo "<p>Customer data inserted successfully.</p>";
    } else {
        echo "<p>Error inserting customer data: " . $conn->error . "</p>";
    }
} else {
    echo "<p>Customer data already exists.</p>";
}

// Check if admin table is empty
$result = $conn->query("SELECT COUNT(*) as count FROM `admin`");
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    // Insert admin data
    $sql = "INSERT INTO `admin` (`Username`, `Password`, `Name`, `Email`) VALUES
    ('admin', '$2y$10$qeS0HEh7urRAiMHxgVjkNu.lQR6zRsmRpDiZrPjVcNlOYHkLgPkry', 'System Administrator', '<EMAIL>')";

    if ($conn->query($sql) === TRUE) {
        echo "<p>Admin data inserted successfully.</p>";
    } else {
        echo "<p>Error inserting admin data: " . $conn->error . "</p>";
    }
} else {
    echo "<p>Admin data already exists.</p>";
}

// Check if account table is empty
$result = $conn->query("SELECT COUNT(*) as count FROM `account`");
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    // Insert account data
    $sql = "INSERT INTO `account` (`AccountID`, `CustomerID`, `BranchID`, `AccountType`, `Balance`) VALUES
    (101, 1, 1, 'Savings', 5000.00),
    (102, 1, 1, 'Checking', 3500.00),
    (103, 2, 2, 'Savings', 7500.00),
    (104, 2, 2, 'Investment', 15000.00),
    (105, 3, 3, 'Savings', 4200.00)";

    if ($conn->query($sql) === TRUE) {
        echo "<p>Account data inserted successfully.</p>";
    } else {
        echo "<p>Error inserting account data: " . $conn->error . "</p>";
    }
} else {
    echo "<p>Account data already exists.</p>";
}

// Check if transaction table is empty
$result = $conn->query("SELECT COUNT(*) as count FROM `transaction`");
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    // Insert transaction data
    $sql = "INSERT INTO `transaction` (`TransactionID`, `AccountID`, `Amount`, `Date`, `Type`) VALUES
    (1, 101, 1000.00, '2025-01-15', 'Credit'),
    (2, 101, 500.00, '2025-01-20', 'Debit'),
    (3, 102, 1500.00, '2025-01-22', 'Credit'),
    (4, 101, 2000.00, '2025-01-25', 'Credit'),
    (5, 103, 1000.00, '2025-01-26', 'Credit'),
    (6, 103, 300.00, '2025-01-28', 'Debit'),
    (7, 104, 5000.00, '2025-01-30', 'Credit'),
    (8, 105, 1200.00, '2025-02-01', 'Credit'),
    (9, 102, 800.00, '2025-02-03', 'Debit'),
    (10, 101, 1500.00, '2025-02-05', 'Transfer')";

    if ($conn->query($sql) === TRUE) {
        echo "<p>Transaction data inserted successfully.</p>";
    } else {
        echo "<p>Error inserting transaction data: " . $conn->error . "</p>";
    }
} else {
    echo "<p>Transaction data already exists.</p>";
}

// Add foreign key constraints
echo "<h2>Adding Foreign Key Constraints</h2>";

// Add account foreign keys
$sql = "ALTER TABLE `account`
  ADD CONSTRAINT `account_ibfk_1` FOREIGN KEY (`CustomerID`) REFERENCES `customer` (`CustomerID`),
  ADD CONSTRAINT `account_ibfk_2` FOREIGN KEY (`BranchID`) REFERENCES `branch` (`BranchID`)";

if ($conn->query($sql) === TRUE) {
    echo "<p>Account foreign keys added successfully.</p>";
} else {
    echo "<p>Error adding account foreign keys (may already exist): " . $conn->error . "</p>";
}

// Add transaction foreign keys
$sql = "ALTER TABLE `transaction`
  ADD CONSTRAINT `transaction_ibfk_1` FOREIGN KEY (`AccountID`) REFERENCES `account` (`AccountID`)";

if ($conn->query($sql) === TRUE) {
    echo "<p>Transaction foreign keys added successfully.</p>";
} else {
    echo "<p>Error adding transaction foreign keys (may already exist): " . $conn->error . "</p>";
}

// Add pending_account foreign keys
$sql = "ALTER TABLE `pending_account`
  ADD CONSTRAINT `pending_account_ibfk_1` FOREIGN KEY (`CustomerID`) REFERENCES `customer` (`CustomerID`),
  ADD CONSTRAINT `pending_account_ibfk_2` FOREIGN KEY (`BranchID`) REFERENCES `branch` (`BranchID`)";

if ($conn->query($sql) === TRUE) {
    echo "<p>Pending account foreign keys added successfully.</p>";
} else {
    echo "<p>Error adding pending account foreign keys (may already exist): " . $conn->error . "</p>";
}

echo "<p><a href='index.php'>Go to Homepage</a></p>";
echo "<p><a href='signup.php'>Go to Signup Page</a></p>";
echo "<p><a href='check_database.php'>Check Database Structure</a></p>";

$conn->close();
?>
