<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['customer_id'])) {
    header("Location: index.php");
    exit();
}

require_once 'config/db_connect.php';

// Get customer information
$customer_id = $_SESSION['customer_id'];
$customer_name = $_SESSION['customer_name'];

// Get accounts for this customer
$accounts_sql = "SELECT AccountID, AccountType, Balance FROM account WHERE CustomerID = $customer_id";
$accounts_result = $conn->query($accounts_sql);

// Build accounts array
$accounts = array();
if ($accounts_result->num_rows > 0) {
    while($row = $accounts_result->fetch_assoc()) {
        $accounts[$row['AccountID']] = array(
            'type' => $row['AccountType'],
            'balance' => $row['Balance']
        );
    }
}

$message = '';
$error = '';

// Process transfer form
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $from_account = intval($_POST['from_account']);
    $to_account = intval($_POST['to_account']);
    $amount = floatval($_POST['amount']);
    $current_date = date('Y-m-d');
    
    // Validate transfer
    if ($from_account == $to_account) {
        $error = "Cannot transfer to the same account";
    } elseif ($amount <= 0) {
        $error = "Amount must be greater than zero";
    } elseif (!isset($accounts[$from_account]) || !isset($accounts[$to_account])) {
        $error = "Invalid account selected";
    } elseif ($accounts[$from_account]['balance'] < $amount) {
        $error = "Insufficient funds in the source account";
    } else {
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Deduct from source account
            $update_from = "UPDATE account SET Balance = Balance - $amount WHERE AccountID = $from_account";
            $conn->query($update_from);
            
            // Add to destination account
            $update_to = "UPDATE account SET Balance = Balance + $amount WHERE AccountID = $to_account";
            $conn->query($update_to);
            
            // Record debit transaction
            $insert_debit = "INSERT INTO transaction (AccountID, Amount, Date, Type) 
                            VALUES ($from_account, $amount, '$current_date', 'Debit')";
            $conn->query($insert_debit);
            
            // Record credit transaction
            $insert_credit = "INSERT INTO transaction (AccountID, Amount, Date, Type) 
                            VALUES ($to_account, $amount, '$current_date', 'Credit')";
            $conn->query($insert_credit);
            
            // Commit transaction
            $conn->commit();
            
            $message = "Transfer of $" . number_format($amount, 2) . " completed successfully";
            
            // Update accounts array with new balances
            $accounts[$from_account]['balance'] -= $amount;
            $accounts[$to_account]['balance'] += $amount;
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $error = "Transfer failed: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Transfer Funds</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Banking System</h1>
            <div class="user-info">
                <p>Welcome, <?php echo htmlspecialchars($customer_name); ?></p>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </header>
        
        <nav>
            <ul>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="transactions.php">Transactions</a></li>
                <li><a href="transfer.php" class="active">Transfer Funds</a></li>
            </ul>
        </nav>
        
        <main>
            <section class="transfer-section">
                <h2>Transfer Funds</h2>
                
                <?php if (!empty($message)): ?>
                    <div class="success-message"><?php echo $message; ?></div>
                <?php endif; ?>
                
                <?php if (!empty($error)): ?>
                    <div class="error-message"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if (count($accounts) >= 2): ?>
                    <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" id="transfer-form">
                        <div class="form-group">
                            <label for="from_account">From Account:</label>
                            <select name="from_account" id="from_account" required>
                                <option value="">Select Account</option>
                                <?php foreach($accounts as $id => $account): ?>
                                    <option value="<?php echo $id; ?>">
                                        <?php echo htmlspecialchars($account['type']); ?> - Balance: $<?php echo number_format($account['balance'], 2); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="to_account">To Account:</label>
                            <select name="to_account" id="to_account" required>
                                <option value="">Select Account</option>
                                <?php foreach($accounts as $id => $account): ?>
                                    <option value="<?php echo $id; ?>">
                                        <?php echo htmlspecialchars($account['type']); ?> - Balance: $<?php echo number_format($account['balance'], 2); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="amount">Amount:</label>
                            <input type="number" id="amount" name="amount" min="0.01" step="0.01" required>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn">Transfer Funds</button>
                        </div>
                    </form>
                <?php else: ?>
                    <p>You need at least two accounts to make a transfer. Please contact your branch to open additional accounts.</p>
                <?php endif; ?>
            </section>
        </main>
        
        <footer>
            <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
        </footer>
    </div>
    
    <script src="js/script.js"></script>
</body>
</html>

<?php
$conn->close();
?>
