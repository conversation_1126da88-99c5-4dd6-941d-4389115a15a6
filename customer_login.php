<?php
session_start();

// Redirect to dashboard if already logged in
if (isset($_SESSION['customer_id'])) {
    header("Location: dashboard.php");
    exit();
}

// Process login form
$error = "";
$verification_needed = false;
$verification_email = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    require_once 'config/db_connect.php';

    $email = $conn->real_escape_string($_POST['email']);
    $phone = $conn->real_escape_string($_POST['phone']);

    // Validate login credentials
    $sql = "SELECT CustomerID, Name, Email, Status, IsVerified FROM customer WHERE Email = '$email' AND Phone = '$phone'";
    $result = $conn->query($sql);

    if ($result->num_rows == 1) {
        $row = $result->fetch_assoc();

        // Check if account is active and verified
        if ($row['Status'] != 'active') {
            if ($row['Status'] == 'pending') {
                $error = "Your account is pending approval by an administrator. Please check back later.";
            } else {
                $error = "Your account is not active. Please contact support for assistance.";
            }
        } elseif (!$row['IsVerified']) {
            $error = "Please verify your email address before logging in.";
            $verification_needed = true;
            $verification_email = $row['Email'];
        } else {
            $_SESSION['customer_id'] = $row['CustomerID'];
            $_SESSION['customer_name'] = $row['Name'];
            $_SESSION['customer_email'] = $row['Email'];

            header("Location: dashboard.php");
            exit();
        }
    } else {
        $error = "Invalid email or phone number";
    }

    $conn->close();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Customer Login</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/landing.css">
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="css/enhanced-style.css">
    <link rel="stylesheet" href="css/banking-patterns.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Banking System</h1>
            <nav class="top-nav">
                <ul>
                    <li><a href="index.php">Home</a></li>
                    <li><a href="customer_login.php" class="active">Customer Login</a></li>
                    <li><a href="admin/login.php">Admin Login</a></li>
                    <li><a href="signup.php">Sign Up</a></li>
                </ul>
            </nav>
        </header>

        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <h2>Customer Login</h2>
                    <p>Access your accounts securely</p>
                </div>

                <?php if (!empty($error)): ?>
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo $error; ?>
                    </div>

                    <?php if ($verification_needed): ?>
                    <div class="verification-help">
                        <h3>How to Verify Your Email</h3>
                        <ol>
                            <li>Check your email inbox for a message from Banking System</li>
                            <li>Open the email and click on the verification link</li>
                            <li>If you can't find the email, check your spam/junk folder</li>
                            <li>Still can't find it? <a href="verify_resend.php?email=<?php echo urlencode($verification_email); ?>">Click here</a> to resend the verification email</li>
                        </ol>
                    </div>
                    <?php endif; ?>
                <?php endif; ?>

                <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="login-form">
                    <div class="form-group">
                        <label for="email">
                            <i class="fas fa-envelope"></i>
                            Email Address
                        </label>
                        <input type="email" id="email" name="email" placeholder="Enter your email" required>
                    </div>

                    <div class="form-group">
                        <label for="phone">
                            <i class="fas fa-phone"></i>
                            Phone Number
                        </label>
                        <input type="text" id="phone" name="phone" placeholder="Enter your phone number" required>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn-login">
                            <i class="fas fa-sign-in-alt"></i>
                            Login
                        </button>
                    </div>
                </form>

                <div class="login-options">
                    <a href="verify_resend.php" class="option-link">
                        <i class="fas fa-envelope"></i>
                        Resend Verification Email
                    </a>
                    <a href="contact.php" class="option-link">
                        <i class="fas fa-question-circle"></i>
                        Need Help?
                    </a>
                </div>

                <div class="login-footer">
                    <p>Don't have an account? <a href="signup.php" class="signup-link">Sign up now</a></p>
                </div>
            </div>

            <div class="login-info">
                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Secure Banking</h3>
                    <p>Your security is our priority. We use advanced encryption to protect your information.</p>
                </div>

                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Mobile Banking</h3>
                    <p>Access your accounts anytime, anywhere with our responsive banking platform.</p>
                </div>

                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3>24/7 Support</h3>
                    <p>Our customer service team is always ready to assist you with any questions.</p>
                </div>
            </div>
        </div>

        <footer>
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Banking System</h3>
                    <p>Your trusted financial partner since 2025.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="customer_login.php">Customer Login</a></li>
                        <li><a href="admin/login.php">Admin Login</a></li>
                        <li><a href="signup.php">Sign Up</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Us</h3>
                    <p>Email: <EMAIL></p>
                    <p>Phone: (*************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
            </div>
        </footer>
    </div>

    <style>
        .verification-help {
            background-color: #e8f5e9;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
        }

        .verification-help h3 {
            color: #2e7d32;
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .verification-help ol {
            margin: 0;
            padding-left: 20px;
        }

        .verification-help li {
            margin-bottom: 8px;
            color: #555;
        }

        .verification-help a {
            color: #1a237e;
            font-weight: bold;
            text-decoration: underline;
        }
    </style>
</body>
</html>
