<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config/db_connect.php';

// Get admin information
$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];

// Check if customer ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: customers.php");
    exit();
}

$customer_id = intval($_GET['id']);

// Get customer details
$customer_sql = "SELECT * FROM customer WHERE CustomerID = $customer_id";
$customer_result = $conn->query($customer_sql);

if ($customer_result->num_rows == 0) {
    header("Location: customers.php");
    exit();
}

$customer = $customer_result->fetch_assoc();

// Get customer accounts
$accounts_sql = "SELECT a.*, b.BranchName 
                FROM account a 
                JOIN branch b ON a.BranchID = b.BranchID 
                WHERE a.CustomerID = $customer_id";
$accounts_result = $conn->query($accounts_sql);

// Get pending account requests
$pending_sql = "SELECT pa.*, b.BranchName 
               FROM pending_account pa 
               JOIN branch b ON pa.BranchID = b.BranchID 
               WHERE pa.CustomerID = $customer_id";
$pending_result = $conn->query($pending_sql);

// Get recent transactions
$transactions_sql = "SELECT t.*, a.AccountType 
                    FROM transaction t 
                    JOIN account a ON t.AccountID = a.AccountID 
                    WHERE a.CustomerID = $customer_id 
                    ORDER BY t.Date DESC LIMIT 10";
$transactions_result = $conn->query($transactions_sql);

// Process customer status update
$message = "";
$error = "";

if (isset($_POST['update_status'])) {
    $new_status = $conn->real_escape_string($_POST['status']);
    
    // Update customer status
    $update_sql = "UPDATE customer SET Status = '$new_status' WHERE CustomerID = $customer_id";
    
    if ($conn->query($update_sql)) {
        $message = "Customer status updated successfully!";
        
        // Refresh customer data
        $customer_result = $conn->query($customer_sql);
        $customer = $customer_result->fetch_assoc();
    } else {
        $error = "Error updating customer status: " . $conn->error;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Customer Details</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body>
    <div class="container">
        <header class="admin-header">
            <h1>Banking System Admin</h1>
            <div class="user-info">
                <p>Welcome, <?php echo htmlspecialchars($admin_name); ?></p>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </header>
        
        <nav class="admin-nav">
            <ul>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="customers.php" class="active">Manage Customers</a></li>
                <li><a href="accounts.php">Manage Accounts</a></li>
                <li><a href="transactions.php">Transactions</a></li>
                <li><a href="reports.php">Reports</a></li>
            </ul>
        </nav>
        
        <main class="admin-main">
            <div class="breadcrumb">
                <a href="dashboard.php">Dashboard</a> &gt; 
                <a href="customers.php">Customers</a> &gt; 
                <span>Customer Details</span>
            </div>
            
            <h2>Customer Details</h2>
            
            <?php if (!empty($message)): ?>
                <div class="success-message"><?php echo $message; ?></div>
            <?php endif; ?>
            
            <?php if (!empty($error)): ?>
                <div class="error-message"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <div class="customer-profile">
                <div class="profile-header">
                    <h3><?php echo htmlspecialchars($customer['Name']); ?></h3>
                    <span class="customer-id">ID: <?php echo $customer['CustomerID']; ?></span>
                    <span class="status-badge status-<?php echo $customer['Status']; ?>">
                        <?php echo ucfirst($customer['Status']); ?>
                    </span>
                </div>
                
                <div class="profile-content">
                    <div class="profile-section">
                        <h4>Personal Information</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Email:</label>
                                <span><?php echo htmlspecialchars($customer['Email']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Phone:</label>
                                <span><?php echo htmlspecialchars($customer['Phone']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Address:</label>
                                <span><?php echo htmlspecialchars($customer['Address']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Registration Date:</label>
                                <span><?php echo !empty($customer['RegistrationDate']) ? date('M d, Y', strtotime($customer['RegistrationDate'])) : 'N/A'; ?></span>
                            </div>
                            <div class="info-item">
                                <label>Email Verified:</label>
                                <span><?php echo $customer['IsVerified'] ? 'Yes' : 'No'; ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="profile-section">
                        <h4>Update Status</h4>
                        <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . "?id=" . $customer_id); ?>" class="status-form">
                            <div class="form-group">
                                <label for="status">Status:</label>
                                <select id="status" name="status">
                                    <option value="active" <?php echo ($customer['Status'] == 'active') ? 'selected' : ''; ?>>Active</option>
                                    <option value="pending" <?php echo ($customer['Status'] == 'pending') ? 'selected' : ''; ?>>Pending</option>
                                    <option value="suspended" <?php echo ($customer['Status'] == 'suspended') ? 'selected' : ''; ?>>Suspended</option>
                                </select>
                            </div>
                            <button type="submit" name="update_status" class="btn">Update Status</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="customer-accounts">
                <h3>Customer Accounts</h3>
                
                <?php if ($accounts_result->num_rows > 0): ?>
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Account ID</th>
                                <th>Account Type</th>
                                <th>Branch</th>
                                <th>Balance</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($account = $accounts_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $account['AccountID']; ?></td>
                                    <td><?php echo htmlspecialchars($account['AccountType']); ?></td>
                                    <td><?php echo htmlspecialchars($account['BranchName']); ?></td>
                                    <td>$<?php echo number_format($account['Balance'], 2); ?></td>
                                    <td class="actions">
                                        <a href="account_details.php?id=<?php echo $account['AccountID']; ?>" class="btn btn-sm">View Details</a>
                                        <a href="transactions.php?account_id=<?php echo $account['AccountID']; ?>" class="btn btn-sm">Transactions</a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>No accounts found for this customer.</p>
                <?php endif; ?>
            </div>
            
            <div class="pending-requests">
                <h3>Pending Account Requests</h3>
                
                <?php if ($pending_result->num_rows > 0): ?>
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Request ID</th>
                                <th>Account Type</th>
                                <th>Branch</th>
                                <th>Initial Deposit</th>
                                <th>Request Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($request = $pending_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $request['RequestID']; ?></td>
                                    <td><?php echo htmlspecialchars($request['AccountType']); ?></td>
                                    <td><?php echo htmlspecialchars($request['BranchName']); ?></td>
                                    <td>$<?php echo number_format($request['InitialDeposit'], 2); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($request['RequestDate'])); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $request['Status']; ?>">
                                            <?php echo ucfirst($request['Status']); ?>
                                        </span>
                                    </td>
                                    <td class="actions">
                                        <?php if ($request['Status'] == 'pending'): ?>
                                            <a href="accounts.php?action=approve&id=<?php echo $request['RequestID']; ?>&view=pending" class="btn btn-sm btn-success">Approve</a>
                                            <a href="accounts.php?action=reject&id=<?php echo $request['RequestID']; ?>&view=pending" class="btn btn-sm btn-danger">Reject</a>
                                        <?php else: ?>
                                            <span>No actions available</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>No pending account requests for this customer.</p>
                <?php endif; ?>
            </div>
            
            <div class="recent-transactions">
                <h3>Recent Transactions</h3>
                
                <?php if ($transactions_result->num_rows > 0): ?>
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Account</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($transaction = $transactions_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $transaction['TransactionID']; ?></td>
                                    <td><?php echo $transaction['AccountID']; ?> (<?php echo $transaction['AccountType']; ?>)</td>
                                    <td>
                                        <span class="<?php echo strtolower($transaction['Type']); ?>">
                                            <?php echo $transaction['Type']; ?>
                                        </span>
                                    </td>
                                    <td>$<?php echo number_format($transaction['Amount'], 2); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($transaction['Date'])); ?></td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                    <a href="transactions.php?customer_id=<?php echo $customer_id; ?>" class="view-all">View All Transactions</a>
                <?php else: ?>
                    <p>No transactions found for this customer.</p>
                <?php endif; ?>
            </div>
            
            <div class="action-buttons">
                <a href="customers.php" class="btn">Back to Customers</a>
            </div>
        </main>
        
        <footer>
            <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
        </footer>
    </div>
    
    <script src="../js/admin.js"></script>
</body>
</html>

<?php
$conn->close();
?>
