<?php
/**
 * Token Service for Banking System
 * 
 * This file contains functions for generating and validating secure tokens
 * for email verification and other security purposes.
 */

/**
 * Generate a secure random token
 * 
 * @param int $length Length of the token (default: 32)
 * @return string Secure random token
 */
function generate_token($length = 32) {
    // Use random_bytes for cryptographically secure random bytes
    if (function_exists('random_bytes')) {
        return bin2hex(random_bytes($length / 2));
    }
    
    // Fallback to openssl_random_pseudo_bytes if random_bytes is not available
    if (function_exists('openssl_random_pseudo_bytes')) {
        return bin2hex(openssl_random_pseudo_bytes($length / 2));
    }
    
    // Last resort fallback (less secure)
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $token = '';
    
    for ($i = 0; $i < $length; $i++) {
        $token .= $characters[mt_rand(0, strlen($characters) - 1)];
    }
    
    return $token;
}

/**
 * Generate a verification token for email verification
 * 
 * @return string Verification token
 */
function generate_verification_token() {
    return generate_token(64);
}

/**
 * Store a verification token in the database
 * 
 * @param mysqli $conn Database connection
 * @param int $customer_id Customer ID
 * @param string $token Verification token
 * @param int $expiry_hours Token expiry in hours (default: 24)
 * @return bool Whether the token was stored successfully
 */
function store_verification_token($conn, $customer_id, $token, $expiry_hours = 24) {
    // Calculate expiry timestamp
    $expiry_timestamp = date('Y-m-d H:i:s', strtotime("+{$expiry_hours} hours"));
    
    // Update customer record with verification token and expiry
    $sql = "UPDATE customer SET 
            VerificationCode = ?, 
            VerificationExpiry = ? 
            WHERE CustomerID = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ssi', $token, $expiry_timestamp, $customer_id);
    $result = $stmt->execute();
    $stmt->close();
    
    return $result;
}

/**
 * Validate a verification token
 * 
 * @param mysqli $conn Database connection
 * @param string $token Verification token
 * @return array Result with status, message, and customer data if valid
 */
function validate_verification_token($conn, $token) {
    // Sanitize token
    $token = $conn->real_escape_string($token);
    
    // Get current timestamp
    $current_timestamp = date('Y-m-d H:i:s');
    
    // Check if token exists and is not expired
    $sql = "SELECT CustomerID, Name, Email, IsVerified, VerificationExpiry 
            FROM customer 
            WHERE VerificationCode = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('s', $token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return [
            'status' => 'error',
            'message' => 'Invalid verification token.',
            'customer' => null
        ];
    }
    
    $customer = $result->fetch_assoc();
    $stmt->close();
    
    // Check if already verified
    if ($customer['IsVerified']) {
        return [
            'status' => 'info',
            'message' => 'Your email has already been verified. You can now login.',
            'customer' => $customer
        ];
    }
    
    // Check if token is expired
    if ($customer['VerificationExpiry'] && $customer['VerificationExpiry'] < $current_timestamp) {
        return [
            'status' => 'error',
            'message' => 'Verification token has expired. Please request a new verification email.',
            'customer' => $customer
        ];
    }
    
    return [
        'status' => 'success',
        'message' => 'Valid verification token.',
        'customer' => $customer
    ];
}

/**
 * Mark a customer's email as verified
 * 
 * @param mysqli $conn Database connection
 * @param int $customer_id Customer ID
 * @return bool Whether the update was successful
 */
function mark_email_as_verified($conn, $customer_id) {
    $sql = "UPDATE customer SET 
            IsVerified = 1, 
            VerificationCode = NULL, 
            VerificationExpiry = NULL 
            WHERE CustomerID = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $customer_id);
    $result = $stmt->execute();
    $stmt->close();
    
    return $result;
}

/**
 * Log verification activity for security tracking
 * 
 * @param string $action Action performed (verify/resend)
 * @param int $customer_id Customer ID
 * @param string $email Customer email
 * @param string $status Status of the action (success/error)
 * @param string $ip_address IP address of the request
 * @param string $user_agent User agent of the request
 * @return bool Whether the log was successful
 */
function log_verification_activity($action, $customer_id, $email, $status, $ip_address = '', $user_agent = '') {
    // Create logs directory if it doesn't exist
    $log_dir = __DIR__ . '/../logs';
    if (!file_exists($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    // Create log file path
    $log_file = $log_dir . '/verification_log.txt';
    
    // Get IP address and user agent if not provided
    if (empty($ip_address)) {
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    if (empty($user_agent)) {
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    }
    
    // Format log entry
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] ACTION: {$action} | CUSTOMER_ID: {$customer_id} | EMAIL: {$email} | STATUS: {$status} | IP: {$ip_address} | USER_AGENT: {$user_agent}" . PHP_EOL;
    
    // Write to log file
    return file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX) !== false;
}
