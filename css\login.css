/* Login Page Styles */

/* Login Container */
.login-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin: 30px 0;
}

/* Login Card */
.login-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 30px;
    max-width: 500px;
    width: 100%;
    margin: 0 auto;
}

.login-header {
    text-align: center;
    margin-bottom: 25px;
}

.login-header h2 {
    color: #1a237e;
    margin-bottom: 10px;
    font-size: 1.8rem;
}

.login-header p {
    color: #757575;
    font-size: 1rem;
}

/* Login Form */
.login-form {
    margin-bottom: 20px;
}

.login-form .form-group {
    margin-bottom: 20px;
}

.login-form label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.login-form label i {
    margin-right: 8px;
    color: #3f51b5;
}

.login-form input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.login-form input:focus {
    border-color: #3f51b5;
    box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2);
    outline: none;
}

.login-form input::placeholder {
    color: #aaa;
}

.btn-login {
    width: 100%;
    padding: 12px;
    background-color: #1a237e;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
}

.btn-login:hover {
    background-color: #3949ab;
}

.btn-login i {
    margin-right: 8px;
}

/* Login Options */
.login-options {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.option-link {
    color: #3f51b5;
    text-decoration: none;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
}

.option-link i {
    margin-right: 5px;
}

.option-link:hover {
    text-decoration: underline;
}

/* Login Footer */
.login-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.login-footer p {
    color: #757575;
    font-size: 0.9rem;
}

.signup-link {
    color: #3f51b5;
    font-weight: 500;
    text-decoration: none;
}

.signup-link:hover {
    text-decoration: underline;
}

/* Login Info Section */
.login-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
    justify-content: center;
}

.info-card {
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 20px;
    transition: transform 0.3s, box-shadow 0.3s;
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.info-icon {
    width: 50px;
    height: 50px;
    background-color: #e8eaf6;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
}

.info-icon i {
    font-size: 1.5rem;
    color: #3f51b5;
}

.info-card h3 {
    color: #1a237e;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.info-card p {
    color: #757575;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Error Message */
.error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 12px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.error-message i {
    margin-right: 10px;
    font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 992px) {
    .login-container {
        grid-template-columns: 1fr;
    }
    
    .login-card {
        max-width: 100%;
    }
    
    .login-info {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .info-card {
        flex: 1 1 calc(33.333% - 20px);
        min-width: 250px;
    }
}

@media (max-width: 768px) {
    .login-options {
        flex-direction: column;
        gap: 10px;
    }
    
    .info-card {
        flex: 1 1 100%;
    }
}
