<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config/db_connect.php';

// Get admin information
$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];

// Check if account ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: accounts.php");
    exit();
}

$account_id = intval($_GET['id']);

// Get account details
$account_sql = "SELECT a.*, c.Name as CustomerName, c.Email as CustomerEmail, c.Phone as CustomerPhone, 
               b.BranchName, b.Location as BranchLocation 
               FROM account a 
               JOIN customer c ON a.CustomerID = c.CustomerID 
               JOIN branch b ON a.BranchID = b.BranchID 
               WHERE a.AccountID = $account_id";
$account_result = $conn->query($account_sql);

if ($account_result->num_rows == 0) {
    header("Location: accounts.php");
    exit();
}

$account = $account_result->fetch_assoc();

// Get account transactions
$transactions_sql = "SELECT * FROM transaction WHERE AccountID = $account_id ORDER BY Date DESC LIMIT 20";
$transactions_result = $conn->query($transactions_sql);

// Calculate account statistics
$total_credits = 0;
$total_debits = 0;
$total_transfers = 0;

$stats_sql = "SELECT Type, SUM(Amount) as Total FROM transaction WHERE AccountID = $account_id GROUP BY Type";
$stats_result = $conn->query($stats_sql);

if ($stats_result->num_rows > 0) {
    while ($row = $stats_result->fetch_assoc()) {
        if ($row['Type'] == 'Credit') {
            $total_credits = $row['Total'];
        } elseif ($row['Type'] == 'Debit') {
            $total_debits = $row['Total'];
        } elseif ($row['Type'] == 'Transfer') {
            $total_transfers = $row['Total'];
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Account Details</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body>
    <div class="container">
        <header class="admin-header">
            <h1>Banking System Admin</h1>
            <div class="user-info">
                <p>Welcome, <?php echo htmlspecialchars($admin_name); ?></p>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </header>
        
        <nav class="admin-nav">
            <ul>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="customers.php">Manage Customers</a></li>
                <li><a href="accounts.php" class="active">Manage Accounts</a></li>
                <li><a href="transactions.php">Transactions</a></li>
                <li><a href="reports.php">Reports</a></li>
            </ul>
        </nav>
        
        <main class="admin-main">
            <div class="breadcrumb">
                <a href="dashboard.php">Dashboard</a> &gt; 
                <a href="accounts.php">Accounts</a> &gt; 
                <span>Account Details</span>
            </div>
            
            <h2>Account Details</h2>
            
            <div class="account-overview">
                <div class="account-header">
                    <h3>Account #<?php echo $account['AccountID']; ?></h3>
                    <span class="account-type"><?php echo htmlspecialchars($account['AccountType']); ?> Account</span>
                </div>
                
                <div class="account-balance">
                    <h4>Current Balance</h4>
                    <div class="balance-amount">$<?php echo number_format($account['Balance'], 2); ?></div>
                </div>
                
                <div class="account-info">
                    <div class="info-section">
                        <h4>Account Information</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Account ID:</label>
                                <span><?php echo $account['AccountID']; ?></span>
                            </div>
                            <div class="info-item">
                                <label>Account Type:</label>
                                <span><?php echo htmlspecialchars($account['AccountType']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Branch:</label>
                                <span><?php echo htmlspecialchars($account['BranchName']); ?> (<?php echo htmlspecialchars($account['BranchLocation']); ?>)</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="info-section">
                        <h4>Customer Information</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Customer:</label>
                                <span>
                                    <a href="customer_details.php?id=<?php echo $account['CustomerID']; ?>">
                                        <?php echo htmlspecialchars($account['CustomerName']); ?>
                                    </a>
                                </span>
                            </div>
                            <div class="info-item">
                                <label>Email:</label>
                                <span><?php echo htmlspecialchars($account['CustomerEmail']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Phone:</label>
                                <span><?php echo htmlspecialchars($account['CustomerPhone']); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="account-stats">
                    <h4>Account Statistics</h4>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <h5>Total Credits</h5>
                            <div class="stat-value credit">$<?php echo number_format($total_credits, 2); ?></div>
                        </div>
                        <div class="stat-card">
                            <h5>Total Debits</h5>
                            <div class="stat-value debit">$<?php echo number_format($total_debits, 2); ?></div>
                        </div>
                        <div class="stat-card">
                            <h5>Total Transfers</h5>
                            <div class="stat-value transfer">$<?php echo number_format($total_transfers, 2); ?></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="account-transactions">
                <h3>Recent Transactions</h3>
                
                <?php if ($transactions_result->num_rows > 0): ?>
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($transaction = $transactions_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $transaction['TransactionID']; ?></td>
                                    <td><?php echo date('M d, Y', strtotime($transaction['Date'])); ?></td>
                                    <td>
                                        <span class="<?php echo strtolower($transaction['Type']); ?>">
                                            <?php echo $transaction['Type']; ?>
                                        </span>
                                    </td>
                                    <td>$<?php echo number_format($transaction['Amount'], 2); ?></td>
                                    <td class="actions">
                                        <a href="transaction_details.php?id=<?php echo $transaction['TransactionID']; ?>" class="btn btn-sm">View Details</a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                    <a href="transactions.php?account_id=<?php echo $account_id; ?>" class="view-all">View All Transactions</a>
                <?php else: ?>
                    <p>No transactions found for this account.</p>
                <?php endif; ?>
            </div>
            
            <div class="action-buttons">
                <a href="accounts.php" class="btn">Back to Accounts</a>
                <a href="transactions.php?account_id=<?php echo $account_id; ?>" class="btn">View All Transactions</a>
                <a href="customer_details.php?id=<?php echo $account['CustomerID']; ?>" class="btn">View Customer Details</a>
            </div>
        </main>
        
        <footer>
            <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
        </footer>
    </div>
    
    <script src="../js/admin.js"></script>
</body>
</html>

<?php
$conn->close();
?>
