-- Database updates for enhanced banking system

-- Create admin table if not exists
CREATE TABLE IF NOT EXISTS `admin` (
  `AdminID` int(11) NOT NULL AUTO_INCREMENT,
  `Username` varchar(50) NOT NULL,
  `Password` varchar(255) NOT NULL,
  `Name` varchar(100) NOT NULL,
  `Email` varchar(100) NOT NULL,
  `LastLogin` datetime DEFAULT NULL,
  PRIMARY KEY (`AdminID`),
  UNIQUE KEY `Username` (`Username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert default admin user (password: admin123)
INSERT INTO `admin` (`Username`, `Password`, `Name`, `Email`) VALUES
('admin', '$2y$10$qeS0HEh7urRAiMHxgVjkNu.lQR6zRsmRpDiZrPjVcNlOYHkLgPkry', 'System Administrator', '<EMAIL>');

-- Add status and verification fields to customer table if they don't exist
ALTER TABLE `customer`
ADD COLUMN IF NOT EXISTS `Status` enum('pending', 'active', 'suspended') NOT NULL DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS `VerificationCode` varchar(32) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `IsVerified` tinyint(1) NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS `RegistrationDate` datetime DEFAULT CURRENT_TIMESTAMP;

-- Update existing customers to active status
UPDATE `customer` SET `Status` = 'active', `IsVerified` = 1;

-- Create pending_account table for new account requests
CREATE TABLE IF NOT EXISTS `pending_account` (
  `RequestID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomerID` int(11) NOT NULL,
  `AccountType` varchar(50) NOT NULL DEFAULT 'Savings',
  `BranchID` int(11) NOT NULL,
  `InitialDeposit` decimal(15,2) NOT NULL DEFAULT 0.00,
  `RequestDate` datetime DEFAULT CURRENT_TIMESTAMP,
  `Status` enum('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
  PRIMARY KEY (`RequestID`),
  KEY `CustomerID` (`CustomerID`),
  KEY `BranchID` (`BranchID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Add constraints if they don't exist
ALTER TABLE `pending_account`
  ADD CONSTRAINT `pending_account_ibfk_1` FOREIGN KEY (`CustomerID`) REFERENCES `customer` (`CustomerID`),
  ADD CONSTRAINT `pending_account_ibfk_2` FOREIGN KEY (`BranchID`) REFERENCES `branch` (`BranchID`);


