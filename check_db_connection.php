<?php
// Output all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database connection parameters
$host = "localhost";
$username = "root";
$password = "";

echo "<h1>Database Connection Check</h1>";

// Try to connect without specifying a database
$conn = new mysqli($host, $username, $password);

// Check connection
if ($conn->connect_error) {
    die("<p>Connection to MySQL server failed: " . $conn->connect_error . "</p>");
} else {
    echo "<p>Successfully connected to MySQL server.</p>";
}

// Check if banking_system database exists
$result = $conn->query("SHOW DATABASES LIKE 'banking_system'");
if ($result->num_rows > 0) {
    echo "<p>The 'banking_system' database exists.</p>";
    
    // Try to connect to the banking_system database
    $conn->close();
    $conn = new mysqli($host, $username, $password, "banking_system");
    
    if ($conn->connect_error) {
        die("<p>Connection to 'banking_system' database failed: " . $conn->connect_error . "</p>");
    } else {
        echo "<p>Successfully connected to 'banking_system' database.</p>";
        
        // Check tables in the database
        $result = $conn->query("SHOW TABLES");
        if ($result->num_rows > 0) {
            echo "<p>Tables in 'banking_system' database:</p>";
            echo "<ul>";
            while($row = $result->fetch_row()) {
                echo "<li>" . $row[0] . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No tables found in 'banking_system' database.</p>";
        }
    }
} else {
    echo "<p>The 'banking_system' database does not exist.</p>";
    
    // Create the database
    if ($conn->query("CREATE DATABASE banking_system")) {
        echo "<p>Created 'banking_system' database.</p>";
    } else {
        echo "<p>Failed to create 'banking_system' database: " . $conn->error . "</p>";
    }
}

// Close connection
$conn->close();

echo "<p><a href='create_branch_directly.php'>Create Branch Table Directly</a></p>";
?>
