document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide success and error messages after 5 seconds
    const messages = document.querySelectorAll('.success-message, .error-message, .info-message');
    
    messages.forEach(function(message) {
        setTimeout(function() {
            message.style.opacity = '0';
            setTimeout(function() {
                message.style.display = 'none';
            }, 500);
        }, 5000);
    });
    
    // Auto-submit filter form when select changes
    const filterForm = document.querySelector('.filter-form');
    if (filterForm) {
        const selectInputs = filterForm.querySelectorAll('select');
        
        selectInputs.forEach(function(select) {
            select.addEventListener('change', function() {
                filterForm.submit();
            });
        });
    }
    
    // Confirmation for delete actions
    const deleteButtons = document.querySelectorAll('.btn-delete');
    
    deleteButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                e.preventDefault();
                return false;
            }
            
            return true;
        });
    });
    
    // Date range validation for filters
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');
    
    if (dateFromInput && dateToInput) {
        dateFromInput.addEventListener('change', function() {
            if (dateToInput.value && dateFromInput.value > dateToInput.value) {
                alert('Start date cannot be after end date');
                dateFromInput.value = '';
            }
        });
        
        dateToInput.addEventListener('change', function() {
            if (dateFromInput.value && dateFromInput.value > dateToInput.value) {
                alert('End date cannot be before start date');
                dateToInput.value = '';
            }
        });
    }
});
