/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Typography */
h1, h2, h3 {
    margin-bottom: 15px;
    color: #1a237e;
}

h1 {
    font-size: 2.2rem;
}

h2 {
    font-size: 1.8rem;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

h3 {
    font-size: 1.4rem;
}

p {
    margin-bottom: 15px;
}

a {
    color: #1a237e;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Header */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 20px;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-info p {
    margin-right: 15px;
    margin-bottom: 0;
}

.logout-btn {
    background-color: #f44336;
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    font-weight: bold;
}

.logout-btn:hover {
    background-color: #d32f2f;
    text-decoration: none;
}

/* Navigation */
nav {
    margin-bottom: 30px;
}

nav ul {
    display: flex;
    list-style: none;
    background-color: #1a237e;
    border-radius: 4px;
}

nav li {
    flex: 1;
    text-align: center;
}

nav a {
    display: block;
    color: white;
    padding: 15px;
    transition: background-color 0.3s;
}

nav a:hover {
    background-color: #3949ab;
    text-decoration: none;
}

nav a.active {
    background-color: #3949ab;
    font-weight: bold;
}

/* Main Content */
main {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 30px;
}

section {
    margin-bottom: 40px;
}

section:last-child {
    margin-bottom: 0;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
}

input, select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

button {
    background-color: #1a237e;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
}

button:hover {
    background-color: #3949ab;
}

.btn {
    display: inline-block;
    background-color: #1a237e;
    color: white;
    padding: 10px 15px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: bold;
    margin-right: 10px;
}

.btn:hover {
    background-color: #3949ab;
    text-decoration: none;
}

.btn-secondary {
    background-color: #757575;
}

.btn-secondary:hover {
    background-color: #616161;
}

/* Login Form */
.login-form {
    max-width: 500px;
    margin: 50px auto;
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.login-form h1 {
    margin-bottom: 10px;
}

.login-form h2 {
    margin-bottom: 30px;
}

/* Accounts */
.accounts-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.account-card {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.account-card h3 {
    color: #1a237e;
    margin-bottom: 15px;
}

.account-number, .branch {
    color: #757575;
    margin-bottom: 10px;
}

.balance {
    font-size: 1.4rem;
    font-weight: bold;
    margin: 15px 0;
}

.account-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

/* Transactions */
.transactions-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.transactions-table th, .transactions-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.transactions-table th {
    background-color: #f5f5f5;
    font-weight: bold;
}

.transactions-table tr:hover {
    background-color: #f9f9f9;
}

.credit {
    color: #4caf50;
    font-weight: bold;
}

.debit {
    color: #f44336;
    font-weight: bold;
}

.view-all {
    display: block;
    text-align: right;
    margin-top: 15px;
    font-weight: bold;
}

/* Filters */
.filters {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.filters form {
    display: flex;
    align-items: flex-end;
    flex-wrap: wrap;
    gap: 15px;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-btn {
    margin-bottom: 0;
}

/* Messages */
.error-message, .success-message {
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.error-message {
    background-color: #ffebee;
    color: #c62828;
    border-left: 5px solid #f44336;
}

.success-message {
    background-color: #e8f5e9;
    color: #2e7d32;
    border-left: 5px solid #4caf50;
}

/* Transaction Form Section */
.transaction-form-section {
    max-width: 600px;
    margin: 0 auto;
}

.account-info {
    background-color: #f5f5f5;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
}

/* Footer */
footer {
    text-align: center;
    padding: 20px 0;
    color: #757575;
    font-size: 0.9rem;
}

/* Profile Section */
.profile-info {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
    margin-bottom: 30px;
}

.profile-details, .profile-form {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.profile-details h3, .profile-form h3 {
    margin-bottom: 20px;
    color: #1a237e;
}

.profile-details p {
    margin-bottom: 15px;
}

.security-section {
    background-color: #e8eaf6;
    border-radius: 8px;
    padding: 20px;
    border-left: 5px solid #3f51b5;
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-active {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.status-pending {
    background-color: #fff8e1;
    color: #ff8f00;
}

.status-suspended {
    background-color: #ffebee;
    color: #c62828;
}

.status-approved {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.status-rejected {
    background-color: #ffebee;
    color: #c62828;
}

/* Form Sections */
.form-section {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.form-section h3 {
    margin-bottom: 20px;
    color: #1a237e;
}

/* Form Footer */
.form-footer {
    margin-top: 20px;
    text-align: center;
}

/* Info Message */
.info-message {
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    background-color: #e3f2fd;
    color: #0d47a1;
    border-left: 5px solid #2196f3;
}

/* Success Message */
.success-message {
    background-color: #e8f5e9;
    color: #2e7d32;
    padding: 12px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    border-left: 5px solid #4caf50;
}

.success-message i {
    margin-right: 10px;
    font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 992px) {
    .profile-info {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    header {
        flex-direction: column;
        text-align: center;
    }

    .user-info {
        margin-top: 15px;
        flex-direction: column;
    }

    .user-info p {
        margin-right: 0;
        margin-bottom: 10px;
    }

    nav ul {
        flex-direction: column;
    }

    .accounts-container {
        grid-template-columns: 1fr;
    }

    .filters form {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
    }
}
