<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['customer_id'])) {
    header("Location: index.php");
    exit();
}

require_once 'config/db_connect.php';

// Get customer information
$customer_id = $_SESSION['customer_id'];
$customer_name = $_SESSION['customer_name'];

// Get customer details
$customer_sql = "SELECT * FROM customer WHERE CustomerID = $customer_id";
$customer_result = $conn->query($customer_sql);
$customer = $customer_result->fetch_assoc();

// Process profile update form
$error = "";
$success = "";
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $name = $conn->real_escape_string($_POST['name']);
    $phone = $conn->real_escape_string($_POST['phone']);
    $address = $conn->real_escape_string($_POST['address']);
    
    // Validate form data
    if (empty($name) || empty($phone) || empty($address)) {
        $error = "All fields are required";
    } else {
        // Update customer information
        $update_sql = "UPDATE customer SET Name = '$name', Phone = '$phone', Address = '$address' WHERE CustomerID = $customer_id";
        
        if ($conn->query($update_sql)) {
            $success = "Your profile has been updated successfully!";
            
            // Update session variable
            $_SESSION['customer_name'] = $name;
            
            // Refresh customer data
            $customer_result = $conn->query($customer_sql);
            $customer = $customer_result->fetch_assoc();
        } else {
            $error = "Error updating profile: " . $conn->error;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - My Profile</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/landing.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Banking System</h1>
            <div class="user-info">
                <p>Welcome, <?php echo htmlspecialchars($customer_name); ?></p>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </header>
        
        <nav>
            <ul>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="transactions.php">Transactions</a></li>
                <li><a href="transfer.php">Transfer Funds</a></li>
                <li><a href="account_request.php">Request New Account</a></li>
                <li><a href="profile.php" class="active">My Profile</a></li>
            </ul>
        </nav>
        
        <main>
            <section class="profile-section">
                <h2>My Profile</h2>
                
                <?php if (!empty($error)): ?>
                    <div class="error-message"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if (!empty($success)): ?>
                    <div class="success-message"><?php echo $success; ?></div>
                <?php endif; ?>
                
                <div class="profile-info">
                    <div class="profile-details">
                        <h3>Account Information</h3>
                        <p><strong>Customer ID:</strong> <?php echo $customer['CustomerID']; ?></p>
                        <p><strong>Email:</strong> <?php echo htmlspecialchars($customer['Email']); ?></p>
                        <p><strong>Status:</strong> 
                            <span class="status-badge status-<?php echo $customer['Status']; ?>">
                                <?php echo ucfirst($customer['Status']); ?>
                            </span>
                        </p>
                        <p><strong>Registration Date:</strong> 
                            <?php echo !empty($customer['RegistrationDate']) ? date('M d, Y', strtotime($customer['RegistrationDate'])) : 'N/A'; ?>
                        </p>
                    </div>
                    
                    <div class="profile-form">
                        <h3>Update Profile</h3>
                        <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" id="profile-form">
                            <div class="form-group">
                                <label for="name">Full Name:</label>
                                <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($customer['Name']); ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="phone">Phone Number:</label>
                                <input type="text" id="phone" name="phone" value="<?php echo htmlspecialchars($customer['Phone']); ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="address">Address:</label>
                                <input type="text" id="address" name="address" value="<?php echo htmlspecialchars($customer['Address']); ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn">Update Profile</button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="security-section">
                    <h3>Security</h3>
                    <p>For security reasons, if you need to change your email address, please contact our customer support.</p>
                </div>
            </section>
        </main>
        
        <footer>
            <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
        </footer>
    </div>
    
    <script src="js/script.js"></script>
</body>
</html>

<?php
$conn->close();
?>
