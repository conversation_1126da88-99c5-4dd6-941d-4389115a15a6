-- Add VerificationExpiry column to customer table
ALTER TABLE customer ADD COLUMN VerificationExpiry DATETIME NULL AFTER VerificationCode;

-- Create email_log table for tracking email activities
CREATE TABLE IF NOT EXISTS email_log (
    LogID INT AUTO_INCREMENT PRIMARY KEY,
    CustomerID INT,
    Email VARCHAR(255) NOT NULL,
    Subject VARCHAR(255) NOT NULL,
    Status ENUM('success', 'error') NOT NULL,
    ErrorMessage TEXT,
    IPAddress VARCHAR(45),
    UserAgent VARCHAR(255),
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (CustomerID) REFERENCES customer(CustomerID) ON DELETE SET NULL
);

-- Create verification_log table for tracking verification activities
CREATE TABLE IF NOT EXISTS verification_log (
    LogID INT AUTO_INCREMENT PRIMARY KEY,
    CustomerID INT,
    Email VARCHAR(255) NOT NULL,
    Action ENUM('verify', 'resend') NOT NULL,
    Status ENUM('success', 'error', 'info') NOT NULL,
    IPAddress VARCHAR(45),
    UserAgent VARCHAR(255),
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (CustomerID) REFERENCES customer(CustomerID) ON DELETE SET NULL
);
