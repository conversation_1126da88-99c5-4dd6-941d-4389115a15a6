<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['customer_id'])) {
    header("Location: index.php");
    exit();
}

require_once 'config/db_connect.php';

// Get customer information
$customer_id = $_SESSION['customer_id'];
$customer_name = $_SESSION['customer_name'];

// Check if transaction ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: transactions.php");
    exit();
}

$transaction_id = intval($_GET['id']);

// Get transaction details
$transaction_sql = "SELECT t.*, a.AccountType, a.Balance, b.BranchName 
                   FROM transaction t 
                   JOIN account a ON t.AccountID = a.AccountID 
                   JOIN branch b ON a.BranchID = b.BranchID 
                   WHERE t.TransactionID = $transaction_id AND a.CustomerID = $customer_id";
$transaction_result = $conn->query($transaction_sql);

// Check if transaction exists and belongs to the logged-in customer
if ($transaction_result->num_rows == 0) {
    header("Location: transactions.php");
    exit();
}

$transaction = $transaction_result->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Transaction Details</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/landing.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Banking System</h1>
            <div class="user-info">
                <p>Welcome, <?php echo htmlspecialchars($customer_name); ?></p>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </header>

        <nav>
            <ul>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="transactions.php" class="active">Transactions</a></li>
                <li><a href="transfer.php">Transfer Funds</a></li>
                <li><a href="account_request.php">Request New Account</a></li>
                <li><a href="profile.php">My Profile</a></li>
            </ul>
        </nav>

        <main>
            <div class="breadcrumb">
                <a href="dashboard.php">Dashboard</a> &gt; 
                <a href="transactions.php">Transactions</a> &gt; 
                <span>Transaction Details</span>
            </div>
            
            <h2>Transaction Details</h2>
            
            <div class="transaction-details">
                <div class="transaction-header">
                    <h3>Transaction #<?php echo $transaction['TransactionID']; ?></h3>
                    <span class="transaction-type <?php echo strtolower($transaction['Type']); ?>">
                        <?php echo $transaction['Type']; ?>
                    </span>
                </div>
                
                <div class="transaction-amount">
                    <h4>Amount</h4>
                    <div class="amount <?php echo strtolower($transaction['Type']); ?>">
                        $<?php echo number_format($transaction['Amount'], 2); ?>
                    </div>
                    <div class="transaction-date">
                        <?php echo date('F d, Y', strtotime($transaction['Date'])); ?>
                    </div>
                </div>
                
                <div class="transaction-info">
                    <div class="info-section">
                        <h4>Transaction Information</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Transaction ID:</label>
                                <span><?php echo $transaction['TransactionID']; ?></span>
                            </div>
                            <div class="info-item">
                                <label>Type:</label>
                                <span class="<?php echo strtolower($transaction['Type']); ?>">
                                    <?php echo $transaction['Type']; ?>
                                </span>
                            </div>
                            <div class="info-item">
                                <label>Date:</label>
                                <span><?php echo date('F d, Y', strtotime($transaction['Date'])); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Amount:</label>
                                <span>$<?php echo number_format($transaction['Amount'], 2); ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="info-section">
                        <h4>Account Information</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Account:</label>
                                <span>
                                    <a href="transactions.php?account=<?php echo $transaction['AccountID']; ?>">
                                        #<?php echo $transaction['AccountID']; ?> (<?php echo $transaction['AccountType']; ?>)
                                    </a>
                                </span>
                            </div>
                            <div class="info-item">
                                <label>Branch:</label>
                                <span><?php echo htmlspecialchars($transaction['BranchName']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Current Balance:</label>
                                <span>$<?php echo number_format($transaction['Balance'], 2); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons" style="margin-top: 20px;">
                <a href="transactions.php" class="btn">Back to Transactions</a>
                <a href="dashboard.php" class="btn">Back to Dashboard</a>
            </div>
        </main>

        <footer>
            <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
        </footer>
    </div>

    <script src="js/script.js"></script>
</body>
</html>

<?php
$conn->close();
?>
