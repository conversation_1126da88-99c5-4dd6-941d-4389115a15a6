/* Landing Page Styles */

/* Top Navigation */
.top-nav {
    margin-bottom: 0;
}

.top-nav ul {
    display: flex;
    list-style: none;
    background-color: transparent;
    border-radius: 0;
}

.top-nav li {
    flex: 0 1 auto;
    margin-left: 20px;
}

.top-nav a {
    display: block;
    color: #1a237e;
    padding: 10px 15px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.top-nav a:hover, .top-nav a.active {
    background-color: #e8eaf6;
    text-decoration: none;
}

/* Header */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 0;
}

/* Main Content */
.landing-main {
    background-color: transparent;
    box-shadow: none;
    padding: 0;
    margin-bottom: 30px;
}

/* Hero Section */
.hero {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 60px 0;
    background-color: #e8eaf6;
    border-radius: 8px;
    margin-top: 30px;
    overflow: hidden;
}

.hero-content {
    flex: 1;
    padding: 0 40px;
}

.hero-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #1a237e;
    border-bottom: none;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: #555;
}

.hero-buttons {
    display: flex;
    gap: 15px;
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Services Section */
.services {
    padding: 60px 0;
}

.services h2 {
    text-align: center;
    margin-bottom: 40px;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.service-card {
    background-color: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.service-icon {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.service-card h3 {
    margin-bottom: 15px;
    color: #1a237e;
}

/* Features Section */
.features {
    padding: 60px 0;
    background-color: #f5f5f5;
    border-radius: 8px;
}

.features h2 {
    text-align: center;
    margin-bottom: 40px;
}

.features-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature {
    padding: 20px;
}

.feature h3 {
    color: #1a237e;
    margin-bottom: 15px;
}

/* CTA Section */
.cta {
    padding: 60px 0;
    text-align: center;
    background-color: #e8eaf6;
    border-radius: 8px;
    margin-bottom: 30px;
}

.cta h2 {
    margin-bottom: 15px;
    border-bottom: none;
}

.cta p {
    margin-bottom: 30px;
    font-size: 1.1rem;
}

/* Footer */
footer {
    background-color: #1a237e;
    color: white;
    padding: 40px 0 0;
    border-radius: 8px 8px 0 0;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    padding: 0 20px 30px;
}

.footer-section h3 {
    color: white;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 10px;
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section a {
    color: #c5cae9;
    text-decoration: none;
}

.footer-section a:hover {
    color: white;
    text-decoration: underline;
}

.footer-bottom {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 15px 0;
    text-align: center;
}

.footer-bottom p {
    margin: 0;
    font-size: 0.9rem;
}

/* Buttons */
.btn-primary {
    background-color: #1a237e;
}

.btn-primary:hover {
    background-color: #3949ab;
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero {
        flex-direction: column;
        padding: 40px 0;
    }
    
    .hero-content {
        padding: 0 20px;
        text-align: center;
        margin-bottom: 30px;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .hero-image {
        padding: 0 20px;
    }
}

@media (max-width: 768px) {
    header {
        flex-direction: column;
        text-align: center;
    }
    
    .top-nav {
        margin-top: 20px;
        width: 100%;
    }
    
    .top-nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .top-nav li {
        margin: 5px;
    }
    
    .services-grid, .features-container {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}
