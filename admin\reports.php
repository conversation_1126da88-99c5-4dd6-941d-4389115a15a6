<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config/db_connect.php';

// Get admin information
$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];

// Get report type
$report_type = isset($_GET['type']) ? $_GET['type'] : 'daily';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-d');
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');

// Set default date range based on report type
if ($report_type == 'daily') {
    $date_from = $date_to = date('Y-m-d');
} elseif ($report_type == 'weekly') {
    $date_from = date('Y-m-d', strtotime('-6 days'));
    $date_to = date('Y-m-d');
} elseif ($report_type == 'monthly') {
    $date_from = date('Y-m-01');
    $date_to = date('Y-m-d');
} elseif ($report_type == 'yearly') {
    $date_from = date('Y-01-01');
    $date_to = date('Y-m-d');
}

// Get transaction summary
$transaction_summary_sql = "SELECT Type, COUNT(*) as Count, SUM(Amount) as Total 
                           FROM transaction 
                           WHERE Date BETWEEN '$date_from' AND '$date_to' 
                           GROUP BY Type";
$transaction_summary_result = $conn->query($transaction_summary_sql);

$transaction_summary = array(
    'Credit' => array('Count' => 0, 'Total' => 0),
    'Debit' => array('Count' => 0, 'Total' => 0),
    'Transfer' => array('Count' => 0, 'Total' => 0)
);

if ($transaction_summary_result->num_rows > 0) {
    while ($row = $transaction_summary_result->fetch_assoc()) {
        $transaction_summary[$row['Type']] = array(
            'Count' => $row['Count'],
            'Total' => $row['Total']
        );
    }
}

// Get account type summary
$account_summary_sql = "SELECT AccountType, COUNT(*) as Count, SUM(Balance) as Total 
                       FROM account 
                       GROUP BY AccountType";
$account_summary_result = $conn->query($account_summary_sql);

// Get customer status summary
$customer_summary_sql = "SELECT Status, COUNT(*) as Count 
                        FROM customer 
                        GROUP BY Status";
$customer_summary_result = $conn->query($customer_summary_sql);

// Get branch summary
$branch_summary_sql = "SELECT b.BranchName, COUNT(a.AccountID) as AccountCount, SUM(a.Balance) as TotalBalance 
                      FROM branch b 
                      LEFT JOIN account a ON b.BranchID = a.BranchID 
                      GROUP BY b.BranchID";
$branch_summary_result = $conn->query($branch_summary_sql);

// Get top transactions
$top_transactions_sql = "SELECT t.*, a.AccountType, c.Name as CustomerName 
                        FROM transaction t 
                        JOIN account a ON t.AccountID = a.AccountID 
                        JOIN customer c ON a.CustomerID = c.CustomerID 
                        WHERE t.Date BETWEEN '$date_from' AND '$date_to' 
                        ORDER BY t.Amount DESC 
                        LIMIT 10";
$top_transactions_result = $conn->query($top_transactions_sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Reports</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body>
    <div class="container">
        <header class="admin-header">
            <h1>Banking System Admin</h1>
            <div class="user-info">
                <p>Welcome, <?php echo htmlspecialchars($admin_name); ?></p>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </header>
        
        <nav class="admin-nav">
            <ul>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="customers.php">Manage Customers</a></li>
                <li><a href="accounts.php">Manage Accounts</a></li>
                <li><a href="transactions.php">Transactions</a></li>
                <li><a href="reports.php" class="active">Reports</a></li>
            </ul>
        </nav>
        
        <main class="admin-main">
            <h2>Reports</h2>
            
            <section class="report-filters">
                <form method="get" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="filter-form">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="type">Report Type:</label>
                            <select id="type" name="type" onchange="this.form.submit()">
                                <option value="daily" <?php echo ($report_type == 'daily') ? 'selected' : ''; ?>>Daily Report</option>
                                <option value="weekly" <?php echo ($report_type == 'weekly') ? 'selected' : ''; ?>>Weekly Report</option>
                                <option value="monthly" <?php echo ($report_type == 'monthly') ? 'selected' : ''; ?>>Monthly Report</option>
                                <option value="yearly" <?php echo ($report_type == 'yearly') ? 'selected' : ''; ?>>Yearly Report</option>
                                <option value="custom" <?php echo ($report_type == 'custom') ? 'selected' : ''; ?>>Custom Date Range</option>
                            </select>
                        </div>
                        
                        <?php if ($report_type == 'custom'): ?>
                            <div class="filter-group">
                                <label for="date_from">From:</label>
                                <input type="date" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                            </div>
                            
                            <div class="filter-group">
                                <label for="date_to">To:</label>
                                <input type="date" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                            </div>
                            
                            <div class="filter-group">
                                <button type="submit" class="btn">Apply</button>
                            </div>
                        <?php endif; ?>
                    </div>
                </form>
            </section>
            
            <section class="report-header">
                <h3>
                    <?php
                    if ($report_type == 'daily') {
                        echo 'Daily Report - ' . date('F d, Y', strtotime($date_from));
                    } elseif ($report_type == 'weekly') {
                        echo 'Weekly Report - ' . date('F d', strtotime($date_from)) . ' to ' . date('F d, Y', strtotime($date_to));
                    } elseif ($report_type == 'monthly') {
                        echo 'Monthly Report - ' . date('F Y', strtotime($date_from));
                    } elseif ($report_type == 'yearly') {
                        echo 'Yearly Report - ' . date('Y', strtotime($date_from));
                    } else {
                        echo 'Custom Report - ' . date('F d, Y', strtotime($date_from)) . ' to ' . date('F d, Y', strtotime($date_to));
                    }
                    ?>
                </h3>
            </section>
            
            <section class="report-summary">
                <h3>Transaction Summary</h3>
                <div class="summary-grid">
                    <div class="summary-card">
                        <h4>Credits</h4>
                        <div class="summary-value credit">$<?php echo number_format($transaction_summary['Credit']['Total'], 2); ?></div>
                        <div class="summary-count"><?php echo $transaction_summary['Credit']['Count']; ?> transactions</div>
                    </div>
                    
                    <div class="summary-card">
                        <h4>Debits</h4>
                        <div class="summary-value debit">$<?php echo number_format($transaction_summary['Debit']['Total'], 2); ?></div>
                        <div class="summary-count"><?php echo $transaction_summary['Debit']['Count']; ?> transactions</div>
                    </div>
                    
                    <div class="summary-card">
                        <h4>Transfers</h4>
                        <div class="summary-value transfer">$<?php echo number_format($transaction_summary['Transfer']['Total'], 2); ?></div>
                        <div class="summary-count"><?php echo $transaction_summary['Transfer']['Count']; ?> transactions</div>
                    </div>
                    
                    <div class="summary-card">
                        <h4>Total Transactions</h4>
                        <div class="summary-value">
                            <?php echo $transaction_summary['Credit']['Count'] + $transaction_summary['Debit']['Count'] + $transaction_summary['Transfer']['Count']; ?>
                        </div>
                        <div class="summary-count">transactions</div>
                    </div>
                </div>
            </section>
            
            <section class="report-details">
                <h3>Account Summary</h3>
                
                <?php if ($account_summary_result->num_rows > 0): ?>
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Account Type</th>
                                <th>Number of Accounts</th>
                                <th>Total Balance</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($row = $account_summary_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($row['AccountType']); ?></td>
                                    <td><?php echo $row['Count']; ?></td>
                                    <td>$<?php echo number_format($row['Total'], 2); ?></td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>No account data available.</p>
                <?php endif; ?>
            </section>
            
            <section class="report-details">
                <h3>Customer Summary</h3>
                
                <?php if ($customer_summary_result->num_rows > 0): ?>
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Status</th>
                                <th>Number of Customers</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($row = $customer_summary_result->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <span class="status-badge status-<?php echo $row['Status']; ?>">
                                            <?php echo ucfirst($row['Status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo $row['Count']; ?></td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>No customer data available.</p>
                <?php endif; ?>
            </section>
            
            <section class="report-details">
                <h3>Branch Summary</h3>
                
                <?php if ($branch_summary_result->num_rows > 0): ?>
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Branch</th>
                                <th>Number of Accounts</th>
                                <th>Total Balance</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($row = $branch_summary_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($row['BranchName']); ?></td>
                                    <td><?php echo $row['AccountCount']; ?></td>
                                    <td>$<?php echo number_format($row['TotalBalance'], 2); ?></td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>No branch data available.</p>
                <?php endif; ?>
            </section>
            
            <section class="report-details">
                <h3>Top Transactions</h3>
                
                <?php if ($top_transactions_result->num_rows > 0): ?>
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Date</th>
                                <th>Customer</th>
                                <th>Account</th>
                                <th>Type</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($transaction = $top_transactions_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $transaction['TransactionID']; ?></td>
                                    <td><?php echo date('M d, Y', strtotime($transaction['Date'])); ?></td>
                                    <td><?php echo htmlspecialchars($transaction['CustomerName']); ?></td>
                                    <td><?php echo $transaction['AccountID']; ?> (<?php echo $transaction['AccountType']; ?>)</td>
                                    <td>
                                        <span class="<?php echo strtolower($transaction['Type']); ?>">
                                            <?php echo $transaction['Type']; ?>
                                        </span>
                                    </td>
                                    <td>$<?php echo number_format($transaction['Amount'], 2); ?></td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>No transaction data available for the selected period.</p>
                <?php endif; ?>
            </section>
            
            <div class="action-buttons">
                <a href="#" onclick="window.print();" class="btn">Print Report</a>
                <a href="dashboard.php" class="btn">Back to Dashboard</a>
            </div>
        </main>
        
        <footer>
            <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
        </footer>
    </div>
    
    <script src="../js/admin.js"></script>
</body>
</html>

<?php
$conn->close();
?>
