<?php
/**
 * Simple autoloader for PHPMailer
 *
 * In a real application, you would use Composer to manage dependencies.
 * This is a simplified version for demonstration purposes.
 */

// For development mode, create mock PHPMailer classes if PHPMailer is not installed
if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
    // Only create these classes if they don't already exist
    if (!class_exists('P<PERSON>Mailer\\PHPMailer\\PHPMailer')) {
        // Create the mock classes in separate files

        // Create directory if it doesn't exist
        if (!file_exists(__DIR__ . '/phpmailer/src')) {
            mkdir(__DIR__ . '/phpmailer/src', 0755, true);
        }

        // Create PHPMailer.php
        $phpmailer_content = '<?php
namespace PHPMailer\\PHPMailer;

class PHPMailer {
    public $ErrorInfo = "";
    public $SMTPDebug = 0;

    // Constants for encryption type
    const ENCRYPTION_STARTTLS = "tls";
    const ENCRYPTION_SMTPS = "ssl";

    // Constants for character sets
    const CHARSET_ISO88591 = "iso-8859-1";
    const CHARSET_UTF8 = "utf-8";

    // Constants for message priorities
    const PRIORITY_HIGH = 1;
    const PRIORITY_NORMAL = 3;
    const PRIORITY_LOW = 5;

    public function isSMTP() {
        return $this;
    }

    public function setFrom($email, $name = "") {
        return $this;
    }

    public function addAddress($email, $name = "") {
        return $this;
    }

    public function addReplyTo($email, $name = "") {
        return $this;
    }

    public function isHTML($isHtml = true) {
        return $this;
    }

    public function addAttachment($path, $name = "") {
        return $this;
    }

    public function send() {
        // In development mode, always return true
        return true;
    }
}';
        file_put_contents(__DIR__ . '/phpmailer/src/PHPMailer.php', $phpmailer_content);

        // Create SMTP.php
        $smtp_content = '<?php
namespace PHPMailer\\PHPMailer;

class SMTP {
    // Constants
    const DEBUG_OFF = 0;
    const DEBUG_CLIENT = 1;
    const DEBUG_SERVER = 2;
    const DEBUG_CONNECTION = 3;
    const DEBUG_LOWLEVEL = 4;
}';
        file_put_contents(__DIR__ . '/phpmailer/src/SMTP.php', $smtp_content);

        // Create Exception.php
        $exception_content = '<?php
namespace PHPMailer\\PHPMailer;

class Exception extends \\Exception {
    // PHPMailer exception class
}';
        file_put_contents(__DIR__ . '/phpmailer/src/Exception.php', $exception_content);
    }
}

// Define the autoloader function
spl_autoload_register(function ($class) {
    // PHPMailer classes
    if (strpos($class, 'PHPMailer\\PHPMailer\\') === 0) {
        // Convert namespace to directory structure
        $class_path = str_replace('\\', '/', $class);
        $class_path = str_replace('PHPMailer/PHPMailer/', '', $class_path);

        // Include the file
        $file = __DIR__ . '/phpmailer/src/' . $class_path . '.php';

        if (file_exists($file)) {
            require_once $file;
            return true;
        }
    }

    return false;
});
?>
