<?php
/**
 * Create Admin User - Manual Fix
 */

require_once 'config/db_connect.php';

echo "<p class='info'>Using database: banking_system_new</p>";

echo "<h1>Create Admin User</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
</style>";

// Check if admin table exists, create if not
$result = $conn->query("SHOW TABLES LIKE 'admin'");
if ($result->num_rows == 0) {
    echo "<p class='info'>Creating admin table...</p>";

    $sql = "CREATE TABLE admin (
        AdminID int(11) NOT NULL AUTO_INCREMENT,
        Username varchar(50) NOT NULL,
        Password varchar(255) NOT NULL,
        Name varchar(100) NOT NULL,
        Email varchar(100) NOT NULL,
        LastLogin datetime DEFAULT NULL,
        PRIMARY KEY (AdminID),
        UNIQUE KEY Username (Username)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    if ($conn->query($sql)) {
        echo "<p class='success'>✅ Admin table created</p>";
    } else {
        echo "<p class='error'>❌ Error creating admin table: " . $conn->error . "</p>";
        exit();
    }
}

// Delete existing admin user if exists
$conn->query("DELETE FROM admin WHERE Username = 'admin'");

// Create new admin user with proper password hash
$username = 'admin';
$password = 'admin123';
$hashed_password = password_hash($password, PASSWORD_DEFAULT);
$name = 'System Administrator';
$email = '<EMAIL>';

echo "<p class='info'>Creating admin user...</p>";
echo "<p>Username: $username</p>";
echo "<p>Password: $password</p>";
echo "<p>Password Hash: " . substr($hashed_password, 0, 30) . "...</p>";

$sql = "INSERT INTO admin (Username, Password, Name, Email) VALUES (?, ?, ?, ?)";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ssss", $username, $hashed_password, $name, $email);

if ($stmt->execute()) {
    echo "<p class='success'>✅ Admin user created successfully!</p>";

    // Verify the user was created
    $verify_sql = "SELECT AdminID, Username, Name FROM admin WHERE Username = 'admin'";
    $verify_result = $conn->query($verify_sql);

    if ($verify_result->num_rows > 0) {
        $admin = $verify_result->fetch_assoc();
        echo "<p class='success'>✅ Verification: Admin user found</p>";
        echo "<p>Admin ID: " . $admin['AdminID'] . "</p>";
        echo "<p>Username: " . $admin['Username'] . "</p>";
        echo "<p>Name: " . $admin['Name'] . "</p>";

        // Test password verification
        $test_result = $conn->query("SELECT Password FROM admin WHERE Username = 'admin'");
        $test_row = $test_result->fetch_assoc();

        if (password_verify('admin123', $test_row['Password'])) {
            echo "<p class='success'>✅ Password verification test passed</p>";
        } else {
            echo "<p class='error'>❌ Password verification test failed</p>";
        }

    } else {
        echo "<p class='error'>❌ Admin user not found after creation</p>";
    }

} else {
    echo "<p class='error'>❌ Error creating admin user: " . $conn->error . "</p>";
}

echo "<h2>Login Instructions</h2>";
echo "<p class='info'>You can now login with:</p>";
echo "<ul>";
echo "<li><strong>Username:</strong> admin</li>";
echo "<li><strong>Password:</strong> admin123</li>";
echo "</ul>";

echo "<p><a href='admin/login.php'>Go to Admin Login</a></p>";
echo "<p><a href='index.php'>Go to Home Page</a></p>";

$conn->close();
?>
