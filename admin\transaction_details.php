<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config/db_connect.php';

// Get admin information
$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];

// Check if transaction ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: transactions.php");
    exit();
}

$transaction_id = intval($_GET['id']);

// Get transaction details
$transaction_sql = "SELECT t.*, a.AccountType, a.CustomerID, c.Name as CustomerName, b.BranchName 
                   FROM transaction t 
                   JOIN account a ON t.AccountID = a.AccountID 
                   JOIN customer c ON a.CustomerID = c.CustomerID 
                   JOIN branch b ON a.BranchID = b.BranchID 
                   WHERE t.TransactionID = $transaction_id";
$transaction_result = $conn->query($transaction_sql);

if ($transaction_result->num_rows == 0) {
    header("Location: transactions.php");
    exit();
}

$transaction = $transaction_result->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Transaction Details</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body>
    <div class="container">
        <header class="admin-header">
            <h1>Banking System Admin</h1>
            <div class="user-info">
                <p>Welcome, <?php echo htmlspecialchars($admin_name); ?></p>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </header>
        
        <nav class="admin-nav">
            <ul>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="customers.php">Manage Customers</a></li>
                <li><a href="accounts.php">Manage Accounts</a></li>
                <li><a href="transactions.php" class="active">Transactions</a></li>
                <li><a href="reports.php">Reports</a></li>
            </ul>
        </nav>
        
        <main class="admin-main">
            <div class="breadcrumb">
                <a href="dashboard.php">Dashboard</a> &gt; 
                <a href="transactions.php">Transactions</a> &gt; 
                <span>Transaction Details</span>
            </div>
            
            <h2>Transaction Details</h2>
            
            <div class="transaction-details">
                <div class="transaction-header">
                    <h3>Transaction #<?php echo $transaction['TransactionID']; ?></h3>
                    <span class="transaction-type <?php echo strtolower($transaction['Type']); ?>">
                        <?php echo $transaction['Type']; ?>
                    </span>
                </div>
                
                <div class="transaction-amount">
                    <h4>Amount</h4>
                    <div class="amount <?php echo strtolower($transaction['Type']); ?>">
                        $<?php echo number_format($transaction['Amount'], 2); ?>
                    </div>
                    <div class="transaction-date">
                        <?php echo date('F d, Y', strtotime($transaction['Date'])); ?>
                    </div>
                </div>
                
                <div class="transaction-info">
                    <div class="info-section">
                        <h4>Transaction Information</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Transaction ID:</label>
                                <span><?php echo $transaction['TransactionID']; ?></span>
                            </div>
                            <div class="info-item">
                                <label>Type:</label>
                                <span class="<?php echo strtolower($transaction['Type']); ?>">
                                    <?php echo $transaction['Type']; ?>
                                </span>
                            </div>
                            <div class="info-item">
                                <label>Date:</label>
                                <span><?php echo date('F d, Y', strtotime($transaction['Date'])); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Amount:</label>
                                <span>$<?php echo number_format($transaction['Amount'], 2); ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="info-section">
                        <h4>Account Information</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Account:</label>
                                <span>
                                    <a href="account_details.php?id=<?php echo $transaction['AccountID']; ?>">
                                        #<?php echo $transaction['AccountID']; ?> (<?php echo $transaction['AccountType']; ?>)
                                    </a>
                                </span>
                            </div>
                            <div class="info-item">
                                <label>Customer:</label>
                                <span>
                                    <a href="customer_details.php?id=<?php echo $transaction['CustomerID']; ?>">
                                        <?php echo htmlspecialchars($transaction['CustomerName']); ?>
                                    </a>
                                </span>
                            </div>
                            <div class="info-item">
                                <label>Branch:</label>
                                <span><?php echo htmlspecialchars($transaction['BranchName']); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <a href="transactions.php" class="btn">Back to Transactions</a>
                <a href="account_details.php?id=<?php echo $transaction['AccountID']; ?>" class="btn">View Account Details</a>
                <a href="customer_details.php?id=<?php echo $transaction['CustomerID']; ?>" class="btn">View Customer Details</a>
            </div>
        </main>
        
        <footer>
            <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
        </footer>
    </div>
    
    <script src="../js/admin.js"></script>
</body>
</html>

<?php
$conn->close();
?>
