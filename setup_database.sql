-- Setup script for banking_system database

-- Table structure for table `branch`
CREATE TABLE IF NOT EXISTS `branch` (
  `BranchID` int(11) NOT NULL AUTO_INCREMENT,
  `BranchName` varchar(100) NOT NULL DEFAULT 'Main Branch',
  `Location` varchar(255) NOT NULL DEFAULT 'Butuan',
  PRIMARY KEY (`BranchID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `customer`
CREATE TABLE IF NOT EXISTS `customer` (
  `CustomerID` int(11) NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) NOT NULL DEFAULT 'Wendelyn Ferrer',
  `Phone` varchar(15) NOT NULL DEFAULT '**************',
  `Email` varchar(100) NOT NULL DEFAULT '<EMAIL>',
  `Address` varchar(255) NOT NULL DEFAULT 'Sibagat',
  `Status` enum('pending', 'active', 'suspended') NOT NULL DEFAULT 'pending',
  `VerificationCode` varchar(32) DEFAULT NULL,
  `IsVerified` tinyint(1) NOT NULL DEFAULT 0,
  `RegistrationDate` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`CustomerID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `account`
CREATE TABLE IF NOT EXISTS `account` (
  `AccountID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomerID` int(11) NOT NULL,
  `BranchID` int(11) NOT NULL,
  `AccountType` varchar(50) NOT NULL DEFAULT 'Savings',
  `Balance` decimal(15,2) NOT NULL DEFAULT 0.00,
  PRIMARY KEY (`AccountID`),
  KEY `CustomerID` (`CustomerID`),
  KEY `BranchID` (`BranchID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `transaction`
CREATE TABLE IF NOT EXISTS `transaction` (
  `TransactionID` int(11) NOT NULL AUTO_INCREMENT,
  `AccountID` int(11) NOT NULL,
  `Amount` decimal(15,2) NOT NULL DEFAULT 5.01,
  `Date` date DEFAULT NULL,
  `Type` varchar(50) NOT NULL DEFAULT 'Credit',
  PRIMARY KEY (`TransactionID`),
  KEY `AccountID` (`AccountID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create admin table
CREATE TABLE IF NOT EXISTS `admin` (
  `AdminID` int(11) NOT NULL AUTO_INCREMENT,
  `Username` varchar(50) NOT NULL,
  `Password` varchar(255) NOT NULL,
  `Name` varchar(100) NOT NULL,
  `Email` varchar(100) NOT NULL,
  `LastLogin` datetime DEFAULT NULL,
  PRIMARY KEY (`AdminID`),
  UNIQUE KEY `Username` (`Username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create pending_account table for new account requests
CREATE TABLE IF NOT EXISTS `pending_account` (
  `RequestID` int(11) NOT NULL AUTO_INCREMENT,
  `CustomerID` int(11) NOT NULL,
  `AccountType` varchar(50) NOT NULL DEFAULT 'Savings',
  `BranchID` int(11) NOT NULL,
  `InitialDeposit` decimal(15,2) NOT NULL DEFAULT 0.00,
  `RequestDate` datetime DEFAULT CURRENT_TIMESTAMP,
  `Status` enum('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
  PRIMARY KEY (`RequestID`),
  KEY `CustomerID` (`CustomerID`),
  KEY `BranchID` (`BranchID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Add constraints
ALTER TABLE `account`
  ADD CONSTRAINT `account_ibfk_1` FOREIGN KEY (`CustomerID`) REFERENCES `customer` (`CustomerID`),
  ADD CONSTRAINT `account_ibfk_2` FOREIGN KEY (`BranchID`) REFERENCES `branch` (`BranchID`);

ALTER TABLE `transaction`
  ADD CONSTRAINT `transaction_ibfk_1` FOREIGN KEY (`AccountID`) REFERENCES `account` (`AccountID`);

ALTER TABLE `pending_account`
  ADD CONSTRAINT `pending_account_ibfk_1` FOREIGN KEY (`CustomerID`) REFERENCES `customer` (`CustomerID`),
  ADD CONSTRAINT `pending_account_ibfk_2` FOREIGN KEY (`BranchID`) REFERENCES `branch` (`BranchID`);

-- Insert default admin user (password: admin123)
INSERT INTO `admin` (`Username`, `Password`, `Name`, `Email`) VALUES
('admin', '$2y$10$qeS0HEh7urRAiMHxgVjkNu.lQR6zRsmRpDiZrPjVcNlOYHkLgPkry', 'System Administrator', '<EMAIL>');

-- Insert branch data
INSERT INTO `branch` (`BranchID`, `BranchName`, `Location`) VALUES
(1, 'Main Branch', 'Butuan'),
(2, 'Downtown Branch', 'Butuan City Center'),
(3, 'Sibagat Branch', 'Sibagat');

-- Insert customer data
INSERT INTO `customer` (`CustomerID`, `Name`, `Phone`, `Email`, `Address`, `Status`, `IsVerified`) VALUES
(1, 'Wendelyn Ferrer', '**************', '<EMAIL>', 'Sibagat', 'active', 1),
(2, 'John Smith', '***********', '<EMAIL>', 'Butuan City', 'active', 1),
(3, 'Maria Garcia', '***********', '<EMAIL>', 'Ampayon', 'active', 1);

-- Insert account data
INSERT INTO `account` (`AccountID`, `CustomerID`, `BranchID`, `AccountType`, `Balance`) VALUES
(101, 1, 1, 'Savings', 5000.00),
(102, 1, 1, 'Checking', 3500.00),
(103, 2, 2, 'Savings', 7500.00),
(104, 2, 2, 'Investment', 15000.00),
(105, 3, 3, 'Savings', 4200.00);

-- Insert transaction data
INSERT INTO `transaction` (`TransactionID`, `AccountID`, `Amount`, `Date`, `Type`) VALUES
(1, 101, 1000.00, '2025-01-15', 'Credit'),
(2, 101, 500.00, '2025-01-20', 'Debit'),
(3, 102, 1500.00, '2025-01-22', 'Credit'),
(4, 101, 2000.00, '2025-01-25', 'Credit'),
(5, 103, 1000.00, '2025-01-26', 'Credit'),
(6, 103, 300.00, '2025-01-28', 'Debit'),
(7, 104, 5000.00, '2025-01-30', 'Credit'),
(8, 105, 1200.00, '2025-02-01', 'Credit'),
(9, 102, 800.00, '2025-02-03', 'Debit'),
(10, 101, 1500.00, '2025-02-05', 'Transfer');


