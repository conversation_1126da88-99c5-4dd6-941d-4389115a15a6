<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config/db_connect.php';

// Get admin information
$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];

// Get filter parameters
$account_id = isset($_GET['account_id']) ? intval($_GET['account_id']) : 0;
$customer_id = isset($_GET['customer_id']) ? intval($_GET['customer_id']) : 0;
$type_filter = isset($_GET['type']) ? $conn->real_escape_string($_GET['type']) : '';
$date_from = isset($_GET['date_from']) ? $conn->real_escape_string($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? $conn->real_escape_string($_GET['date_to']) : '';

// Build query with filters
$transactions_sql = "SELECT t.TransactionID, t.AccountID, t.Amount, t.Date, t.Type, 
                    a.AccountType, c.CustomerID, c.Name as CustomerName 
                    FROM transaction t 
                    JOIN account a ON t.AccountID = a.AccountID 
                    JOIN customer c ON a.CustomerID = c.CustomerID";

$where_clauses = array();

if ($account_id > 0) {
    $where_clauses[] = "t.AccountID = $account_id";
}

if ($customer_id > 0) {
    $where_clauses[] = "c.CustomerID = $customer_id";
}

if (!empty($type_filter)) {
    $where_clauses[] = "t.Type = '$type_filter'";
}

if (!empty($date_from)) {
    $where_clauses[] = "t.Date >= '$date_from'";
}

if (!empty($date_to)) {
    $where_clauses[] = "t.Date <= '$date_to'";
}

if (!empty($where_clauses)) {
    $transactions_sql .= " WHERE " . implode(" AND ", $where_clauses);
}

$transactions_sql .= " ORDER BY t.Date DESC, t.TransactionID DESC";
$transactions_result = $conn->query($transactions_sql);

// Get account information if filtering by account
$account_info = null;
if ($account_id > 0) {
    $account_sql = "SELECT a.AccountID, a.AccountType, a.Balance, c.Name as CustomerName 
                   FROM account a 
                   JOIN customer c ON a.CustomerID = c.CustomerID 
                   WHERE a.AccountID = $account_id";
    $account_result = $conn->query($account_sql);
    
    if ($account_result->num_rows == 1) {
        $account_info = $account_result->fetch_assoc();
    }
}

// Get customer information if filtering by customer
$customer_info = null;
if ($customer_id > 0 && !$account_info) {
    $customer_sql = "SELECT CustomerID, Name FROM customer WHERE CustomerID = $customer_id";
    $customer_result = $conn->query($customer_sql);
    
    if ($customer_result->num_rows == 1) {
        $customer_info = $customer_result->fetch_assoc();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Transactions</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body>
    <div class="container">
        <header class="admin-header">
            <h1>Banking System Admin</h1>
            <div class="user-info">
                <p>Welcome, <?php echo htmlspecialchars($admin_name); ?></p>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </header>
        
        <nav class="admin-nav">
            <ul>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="customers.php">Manage Customers</a></li>
                <li><a href="accounts.php">Manage Accounts</a></li>
                <li><a href="transactions.php" class="active">Transactions</a></li>
                <li><a href="reports.php">Reports</a></li>
            </ul>
        </nav>
        
        <main class="admin-main">
            <h2>Transaction History</h2>
            
            <?php if ($account_info): ?>
                <div class="info-box">
                    <h3>Account Information</h3>
                    <p><strong>Account ID:</strong> <?php echo $account_info['AccountID']; ?></p>
                    <p><strong>Account Type:</strong> <?php echo htmlspecialchars($account_info['AccountType']); ?></p>
                    <p><strong>Customer:</strong> <?php echo htmlspecialchars($account_info['CustomerName']); ?></p>
                    <p><strong>Current Balance:</strong> $<?php echo number_format($account_info['Balance'], 2); ?></p>
                </div>
            <?php elseif ($customer_info): ?>
                <div class="info-box">
                    <h3>Customer Information</h3>
                    <p><strong>Customer ID:</strong> <?php echo $customer_info['CustomerID']; ?></p>
                    <p><strong>Name:</strong> <?php echo htmlspecialchars($customer_info['Name']); ?></p>
                </div>
            <?php endif; ?>
            
            <section class="filters">
                <form method="get" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="filter-form">
                    <?php if ($account_id > 0): ?>
                        <input type="hidden" name="account_id" value="<?php echo $account_id; ?>">
                    <?php endif; ?>
                    
                    <?php if ($customer_id > 0): ?>
                        <input type="hidden" name="customer_id" value="<?php echo $customer_id; ?>">
                    <?php endif; ?>
                    
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="type">Transaction Type:</label>
                            <select id="type" name="type">
                                <option value="">All Types</option>
                                <option value="Credit" <?php echo ($type_filter == 'Credit') ? 'selected' : ''; ?>>Credit</option>
                                <option value="Debit" <?php echo ($type_filter == 'Debit') ? 'selected' : ''; ?>>Debit</option>
                                <option value="Transfer" <?php echo ($type_filter == 'Transfer') ? 'selected' : ''; ?>>Transfer</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="date_from">Date From:</label>
                            <input type="date" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                        </div>
                        
                        <div class="filter-group">
                            <label for="date_to">Date To:</label>
                            <input type="date" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                        </div>
                    </div>
                    
                    <div class="filter-actions">
                        <button type="submit" class="btn">Apply Filters</button>
                        <a href="transactions.php<?php echo ($account_id > 0) ? '?account_id=' . $account_id : ''; ?><?php echo ($customer_id > 0) ? '?customer_id=' . $customer_id : ''; ?>" class="btn btn-secondary">Reset Filters</a>
                    </div>
                </form>
            </section>
            
            <section class="transactions-list">
                <?php if ($transactions_result->num_rows > 0): ?>
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Date</th>
                                <th>Customer</th>
                                <th>Account</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($transaction = $transactions_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $transaction['TransactionID']; ?></td>
                                    <td><?php echo date('M d, Y', strtotime($transaction['Date'])); ?></td>
                                    <td>
                                        <a href="transactions.php?customer_id=<?php echo $transaction['CustomerID']; ?>">
                                            <?php echo htmlspecialchars($transaction['CustomerName']); ?>
                                        </a>
                                    </td>
                                    <td>
                                        <a href="transactions.php?account_id=<?php echo $transaction['AccountID']; ?>">
                                            <?php echo $transaction['AccountID']; ?> (<?php echo $transaction['AccountType']; ?>)
                                        </a>
                                    </td>
                                    <td>
                                        <span class="<?php echo strtolower($transaction['Type']); ?>">
                                            <?php echo $transaction['Type']; ?>
                                        </span>
                                    </td>
                                    <td>$<?php echo number_format($transaction['Amount'], 2); ?></td>
                                    <td class="actions">
                                        <a href="transaction_details.php?id=<?php echo $transaction['TransactionID']; ?>" class="btn btn-sm">Details</a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>No transactions found matching your criteria.</p>
                <?php endif; ?>
            </section>
        </main>
        
        <footer>
            <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
        </footer>
    </div>
    
    <script src="../js/admin.js"></script>
</body>
</html>

<?php
$conn->close();
?>
