<?php
// Output all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Check Database Case Sensitivity</h1>";

// Database connection parameters
$host = "localhost";
$username = "root";
$password = "";

// Connect to MySQL server
$conn = new mysqli($host, $username, $password);

// Check connection
if ($conn->connect_error) {
    die("<p>Connection to MySQL server failed: " . $conn->connect_error . "</p>");
} else {
    echo "<p>Successfully connected to MySQL server.</p>";
}

// Get all databases
$result = $conn->query("SHOW DATABASES");
echo "<h2>All Databases:</h2>";
echo "<ul>";
while ($row = $result->fetch_row()) {
    echo "<li>" . $row[0] . "</li>";
}
echo "</ul>";

// Check for banking_system database with different cases
$variations = [
    'banking_system',
    'BANKING_SYSTEM',
    'Banking_System',
    'bankingsystem',
    'BankingSystem'
];

echo "<h2>Checking for database with different case variations:</h2>";
echo "<ul>";
foreach ($variations as $db_name) {
    $result = $conn->query("SHOW DATABASES LIKE '$db_name'");
    if ($result->num_rows > 0) {
        echo "<li>Found database: $db_name</li>";
        
        // Try to connect to this database
        $test_conn = new mysqli($host, $username, $password, $db_name);
        if ($test_conn->connect_error) {
            echo "<p>Cannot connect to $db_name: " . $test_conn->connect_error . "</p>";
        } else {
            echo "<p>Successfully connected to $db_name database.</p>";
            
            // Check for branch table
            $table_result = $test_conn->query("SHOW TABLES LIKE 'branch'");
            if ($table_result->num_rows > 0) {
                echo "<p>Branch table exists in $db_name database.</p>";
            } else {
                echo "<p>Branch table does not exist in $db_name database.</p>";
            }
            
            $test_conn->close();
        }
    } else {
        echo "<li>Database not found: $db_name</li>";
    }
}
echo "</ul>";

// Create a fresh database with a different name
$new_db_name = "banking_system_new";
echo "<h2>Creating a fresh database: $new_db_name</h2>";

if ($conn->query("DROP DATABASE IF EXISTS $new_db_name")) {
    echo "<p>Dropped existing $new_db_name database (if any).</p>";
} else {
    echo "<p>Error dropping database: " . $conn->error . "</p>";
}

if ($conn->query("CREATE DATABASE $new_db_name")) {
    echo "<p>Created $new_db_name database.</p>";
    
    // Select the new database
    if (!$conn->select_db($new_db_name)) {
        echo "<p>Error selecting database: " . $conn->error . "</p>";
    } else {
        echo "<p>Successfully selected $new_db_name database.</p>";
        
        // Create branch table
        $sql = "CREATE TABLE branch (
          BranchID int(11) NOT NULL AUTO_INCREMENT,
          BranchName varchar(100) NOT NULL DEFAULT 'Main Branch',
          Location varchar(255) NOT NULL DEFAULT 'Butuan',
          PRIMARY KEY (BranchID)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        
        if ($conn->query($sql)) {
            echo "<p>Branch table created successfully in $new_db_name.</p>";
            
            // Insert branch data
            $sql = "INSERT INTO branch (BranchID, BranchName, Location) VALUES
            (1, 'Main Branch', 'Butuan'),
            (2, 'Downtown Branch', 'Butuan City Center'),
            (3, 'Sibagat Branch', 'Sibagat')";
            
            if ($conn->query($sql)) {
                echo "<p>Branch data inserted successfully in $new_db_name.</p>";
                
                // Create a test file to connect to the new database
                $test_file = 'test_new_db.php';
                $test_content = <<<EOT
<?php
// Output all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Test New Database Connection</h1>";

// Database connection parameters
\$host = "localhost";
\$username = "root";
\$password = "";
\$database = "$new_db_name";

// Create connection
\$conn = new mysqli(\$host, \$username, \$password, \$database);

// Check connection
if (\$conn->connect_error) {
    die("<p>Connection failed: " . \$conn->connect_error . "</p>");
} else {
    echo "<p>Successfully connected to \$database database.</p>";
}

// Get branches
\$branches_sql = "SELECT BranchID, BranchName, Location FROM branch";
\$branches_result = \$conn->query(\$branches_sql);

if (!\$branches_result) {
    echo "<p>Error fetching branches: " . \$conn->error . "</p>";
} else {
    echo "<p>Branch query executed successfully.</p>";
    
    if (\$branches_result->num_rows > 0) {
        echo "<p>Found " . \$branches_result->num_rows . " branches:</p>";
        echo "<ul>";
        while(\$row = \$branches_result->fetch_assoc()) {
            echo "<li>" . \$row['BranchID'] . ": " . \$row['BranchName'] . " (" . \$row['Location'] . ")</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No branches found in the database.</p>";
    }
}

\$conn->close();
?>
EOT;
                
                if (file_put_contents($test_file, $test_content)) {
                    echo "<p>Test file created successfully: <a href='test_new_db.php'>test_new_db.php</a></p>";
                } else {
                    echo "<p>Error creating test file.</p>";
                }
            } else {
                echo "<p>Error inserting branch data: " . $conn->error . "</p>";
            }
        } else {
            echo "<p>Error creating branch table: " . $conn->error . "</p>";
        }
    }
} else {
    echo "<p>Error creating database: " . $conn->error . "</p>";
}

// Close connection
$conn->close();

echo "<p><a href='signup.php'>Try Signup Page Again</a></p>";
echo "<p><a href='index.php'>Go to Homepage</a></p>";
?>
