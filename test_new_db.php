<?php
// Output all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Test New Database Connection</h1>";

// Database connection parameters
$host = "localhost";
$username = "root";
$password = "";
$database = "banking_system_new";

// Create connection
$conn = new mysqli($host, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    die("<p>Connection failed: " . $conn->connect_error . "</p>");
} else {
    echo "<p>Successfully connected to $database database.</p>";
}

// Get branches
$branches_sql = "SELECT BranchID, BranchName, Location FROM branch";
$branches_result = $conn->query($branches_sql);

if (!$branches_result) {
    echo "<p>Error fetching branches: " . $conn->error . "</p>";
} else {
    echo "<p>Branch query executed successfully.</p>";
    
    if ($branches_result->num_rows > 0) {
        echo "<p>Found " . $branches_result->num_rows . " branches:</p>";
        echo "<ul>";
        while($row = $branches_result->fetch_assoc()) {
            echo "<li>" . $row['BranchID'] . ": " . $row['BranchName'] . " (" . $row['Location'] . ")</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No branches found in the database.</p>";
    }
}

$conn->close();
?>