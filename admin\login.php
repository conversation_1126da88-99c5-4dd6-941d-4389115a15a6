<?php
session_start();

// Redirect if already logged in
if (isset($_SESSION['admin_id'])) {
    header("Location: dashboard.php");
    exit();
} elseif (isset($_SESSION['customer_id'])) {
    header("Location: ../dashboard.php");
    exit();
}

// Process login form
$error = "";
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    require_once '../config/db_connect.php';
    
    $username = $conn->real_escape_string($_POST['username']);
    $password = $_POST['password'];
    
    // Validate login credentials
    $sql = "SELECT AdminID, Username, Password, Name FROM admin WHERE Username = '$username'";
    $result = $conn->query($sql);
    
    if ($result->num_rows == 1) {
        $row = $result->fetch_assoc();
        
        // Verify password
        if (password_verify($password, $row['Password'])) {
            $_SESSION['admin_id'] = $row['AdminID'];
            $_SESSION['admin_username'] = $row['Username'];
            $_SESSION['admin_name'] = $row['Name'];
            
            // Update last login time
            $update_login = "UPDATE admin SET LastLogin = NOW() WHERE AdminID = " . $row['AdminID'];
            $conn->query($update_login);
            
            header("Location: dashboard.php");
            exit();
        } else {
            $error = "Invalid username or password";
        }
    } else {
        $error = "Invalid username or password";
    }
    
    $conn->close();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Admin Login</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/landing.css">
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Banking System</h1>
            <nav class="top-nav">
                <ul>
                    <li><a href="../index.php">Home</a></li>
                    <li><a href="../customer_login.php">Customer Login</a></li>
                    <li><a href="login.php" class="active">Admin Login</a></li>
                    <li><a href="../signup.php">Sign Up</a></li>
                </ul>
            </nav>
        </header>
        
        <div class="login-form admin-login">
            <h2>Admin Login</h2>
            
            <?php if (!empty($error)): ?>
                <div class="error-message"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">Login</button>
                </div>
            </form>
            
            <div class="form-footer">
                <p><a href="../index.php">Back to Home</a></p>
            </div>
        </div>
        
        <footer>
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Banking System</h3>
                    <p>Your trusted financial partner since 2025.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="../index.php">Home</a></li>
                        <li><a href="../customer_login.php">Customer Login</a></li>
                        <li><a href="login.php">Admin Login</a></li>
                        <li><a href="../signup.php">Sign Up</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Us</h3>
                    <p>Email: <EMAIL></p>
                    <p>Phone: (*************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
            </div>
        </footer>
    </div>
</body>
</html>
