<?php
// Output all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();

echo "<h1>Direct Admin Login</h1>";

// Database connection
require_once '../config/db_connect.php';

// Get admin user
$result = $conn->query("SELECT * FROM admin WHERE Username = 'admin'");

if ($result && $result->num_rows > 0) {
    $admin = $result->fetch_assoc();
    
    // Set session variables
    $_SESSION['admin_id'] = $admin['AdminID'];
    $_SESSION['admin_username'] = $admin['Username'];
    $_SESSION['admin_name'] = $admin['Name'];
    
    echo "<p>Session variables set:</p>";
    echo "<ul>";
    echo "<li>admin_id: " . $_SESSION['admin_id'] . "</li>";
    echo "<li>admin_username: " . $_SESSION['admin_username'] . "</li>";
    echo "<li>admin_name: " . $_SESSION['admin_name'] . "</li>";
    echo "</ul>";
    
    echo "<p>You are now logged in as admin. <a href='dashboard.php'>Go to Dashboard</a></p>";
} else {
    echo "<p>Admin user not found. <a href='../reset_admin_password.php'>Reset Admin Password</a></p>";
}

// Close connection
$conn->close();
?>
