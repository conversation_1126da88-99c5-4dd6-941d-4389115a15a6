<?php
// Database connection parameters
$host = "localhost";
$username = "root";
$password = "";
$database = "banking_system";

// Create connection without database
$conn = new mysqli($host, $username, $password);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>Setting up Banking System Database</h1>";

// Create database
$sql = "CREATE DATABASE IF NOT EXISTS $database";
if ($conn->query($sql) === TRUE) {
    echo "<p>Database created successfully or already exists.</p>";
} else {
    echo "<p>Error creating database: " . $conn->error . "</p>";
}

// Select the database
$conn->select_db($database);

// Read the SQL file
$sql_file = file_get_contents('setup_database.sql');

// Split the SQL file into individual statements
$statements = explode(';', $sql_file);

// Execute each statement
$success = true;
foreach ($statements as $statement) {
    $statement = trim($statement);
    if (!empty($statement)) {
        if ($conn->query($statement) !== TRUE) {
            echo "<p>Error executing statement: " . $conn->error . "</p>";
            echo "<p>Statement: " . $statement . "</p>";
            $success = false;
        }
    }
}

if ($success) {
    echo "<p>Database setup completed successfully!</p>";
} else {
    echo "<p>There were errors during database setup. Please check the messages above.</p>";
}

echo "<p><a href='index.php'>Go to Homepage</a></p>";

$conn->close();
?>


