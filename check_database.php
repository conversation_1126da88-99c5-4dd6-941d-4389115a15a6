<?php
// Database connection parameters
$host = "localhost";
$username = "root";
$password = "";
$database = "banking_system";

// Create connection
$conn = new mysqli($host, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>Database Structure Check</h1>";

// Get all tables
$result = $conn->query("SHOW TABLES");

if ($result->num_rows > 0) {
    echo "<h2>Tables in the database:</h2>";
    echo "<ul>";
    while($row = $result->fetch_row()) {
        echo "<li>" . $row[0] . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No tables found in the database.</p>";
}

// Check branch table structure
$result = $conn->query("SHOW TABLES LIKE 'branch'");
if ($result->num_rows > 0) {
    echo "<h2>Branch Table Structure:</h2>";
    $result = $conn->query("DESCRIBE branch");
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row["Field"] . "</td>";
        echo "<td>" . $row["Type"] . "</td>";
        echo "<td>" . $row["Null"] . "</td>";
        echo "<td>" . $row["Key"] . "</td>";
        echo "<td>" . $row["Default"] . "</td>";
        echo "<td>" . $row["Extra"] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check branch table data
    $result = $conn->query("SELECT * FROM branch");
    if ($result->num_rows > 0) {
        echo "<h2>Branch Table Data:</h2>";
        echo "<table border='1'>";
        echo "<tr><th>BranchID</th><th>BranchName</th><th>Location</th></tr>";
        while($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row["BranchID"] . "</td>";
            echo "<td>" . $row["BranchName"] . "</td>";
            echo "<td>" . $row["Location"] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No data in branch table.</p>";
    }
} else {
    echo "<p>Branch table does not exist.</p>";
}

echo "<p><a href='index.php'>Go to Homepage</a></p>";

$conn->close();
?>
