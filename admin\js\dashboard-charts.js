// Dashboard Charts

// Global Chart.js configuration
Chart.defaults.font.family = "'Segoe UI', 'Helvetica Neue', Arial, sans-serif";
Chart.defaults.font.size = 12;
Chart.defaults.color = '#666';
Chart.defaults.plugins.tooltip.backgroundColor = 'rgba(0, 0, 0, 0.7)';
Chart.defaults.plugins.legend.labels.usePointStyle = true;

// Store chart instances to destroy and recreate when needed
let transactionTrendsChart = null;
let transactionTypesChart = null;
let accountTypesChart = null;

// Default filter values
let currentPeriod = 'monthly';
let startDate = getDefaultStartDate('monthly');
let endDate = formatDate(new Date());

// Initialize charts when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize date pickers
    initializeDatePickers();

    // Initialize period selector
    initializePeriodSelector();

    // Initialize export buttons
    initializeExportButtons();

    // Load initial charts
    loadTransactionTrendsChart();
    loadTransactionTypesChart();
    loadAccountTypesChart();
});

// Initialize date pickers
function initializeDatePickers() {
    // Set initial values for date inputs
    document.getElementById('start-date').value = startDate;
    document.getElementById('end-date').value = endDate;

    // Add event listeners for date changes
    document.getElementById('start-date').addEventListener('change', function() {
        startDate = this.value;
        refreshAllCharts();
    });

    document.getElementById('end-date').addEventListener('change', function() {
        endDate = this.value;
        refreshAllCharts();
    });
}

// Initialize period selector
function initializePeriodSelector() {
    const periodSelector = document.getElementById('period-selector');

    periodSelector.addEventListener('change', function() {
        currentPeriod = this.value;

        // Update start date based on selected period
        startDate = getDefaultStartDate(currentPeriod);
        document.getElementById('start-date').value = startDate;

        refreshAllCharts();
    });
}

// Get default start date based on period
function getDefaultStartDate(period) {
    const today = new Date();
    let startDate = new Date();

    switch (period) {
        case 'daily':
            startDate.setDate(today.getDate() - 7); // Last 7 days
            break;
        case 'weekly':
            startDate.setDate(today.getDate() - 28); // Last 4 weeks
            break;
        case 'monthly':
            startDate.setMonth(today.getMonth() - 6); // Last 6 months
            break;
        case 'yearly':
            startDate.setFullYear(today.getFullYear() - 2); // Last 2 years
            break;
        default:
            startDate.setDate(today.getDate() - 30); // Default to last 30 days
    }

    return formatDate(startDate);
}

// Format date as YYYY-MM-DD
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// Initialize export buttons
function initializeExportButtons() {
    // Transaction trends export button
    document.getElementById('export-trends-btn').addEventListener('click', function() {
        exportData('transaction_trends');
    });

    // Transaction types export button
    document.getElementById('export-types-btn').addEventListener('click', function() {
        exportData('transaction_types');
    });

    // Account types export button
    document.getElementById('export-accounts-btn').addEventListener('click', function() {
        exportData('account_types');
    });
}

// Export data to Excel
function exportData(exportType) {
    const url = `export_data.php?type=${exportType}&period=${currentPeriod}&start_date=${startDate}&end_date=${endDate}`;
    window.location.href = url;
}

// Refresh all charts
function refreshAllCharts() {
    loadTransactionTrendsChart();
    loadTransactionTypesChart();
    loadAccountTypesChart();
}

// Load transaction trends chart
function loadTransactionTrendsChart() {
    // For testing, use sample data
    fetch(`sample_chart_data.php?chart=transaction_trends&period=${currentPeriod}&start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderTransactionTrendsChart(data);
            } else {
                console.error('Error loading transaction trends data:', data.error);
            }
        })
        .catch(error => {
            console.error('Error fetching transaction trends data:', error);
        });
}

// Render transaction trends chart
function renderTransactionTrendsChart(data) {
    const ctx = document.getElementById('transaction-trends-chart').getContext('2d');

    // Destroy existing chart if it exists
    if (transactionTrendsChart) {
        transactionTrendsChart.destroy();
    }

    // Create new chart
    transactionTrendsChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.labels,
            datasets: data.datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: false
                },
                legend: {
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

// Load transaction types chart
function loadTransactionTypesChart() {
    // For testing, use sample data
    fetch(`sample_chart_data.php?chart=transaction_types&period=${currentPeriod}&start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderTransactionTypesChart(data);
            } else {
                console.error('Error loading transaction types data:', data.error);
            }
        })
        .catch(error => {
            console.error('Error fetching transaction types data:', error);
        });
}

// Render transaction types chart
function renderTransactionTypesChart(data) {
    const ctx = document.getElementById('transaction-types-chart').getContext('2d');

    // Destroy existing chart if it exists
    if (transactionTypesChart) {
        transactionTypesChart.destroy();
    }

    // Create new chart
    transactionTypesChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: data.labels,
            datasets: data.datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: false
                },
                legend: {
                    position: 'right'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            const amount = data.amounts[context.dataIndex];
                            return `${label}: ${value} transactions (${percentage}%) - $${amount.toLocaleString()}`;
                        }
                    }
                }
            }
        }
    });
}

// Load account types chart
function loadAccountTypesChart() {
    // For testing, use sample data
    fetch(`sample_chart_data.php?chart=account_types&period=${currentPeriod}&start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderAccountTypesChart(data);
            } else {
                console.error('Error loading account types data:', data.error);
            }
        })
        .catch(error => {
            console.error('Error fetching account types data:', error);
        });
}

// Render account types chart
function renderAccountTypesChart(data) {
    const ctx = document.getElementById('account-types-chart').getContext('2d');

    // Destroy existing chart if it exists
    if (accountTypesChart) {
        accountTypesChart.destroy();
    }

    // Create new chart
    accountTypesChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.labels,
            datasets: data.datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            plugins: {
                title: {
                    display: false
                },
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = context.raw || 0;
                            const amount = data.amounts[context.dataIndex];
                            return `${value} transactions - $${amount.toLocaleString()}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Transactions'
                    }
                }
            }
        }
    });
}
