# Simple Banking System

A simple banking system web application built with PHP, MySQL, CSS, and JavaScript.

## Features

- User authentication
- Account dashboard
- View account details and balance
- View transaction history
- Make deposits and withdrawals
- Transfer funds between accounts

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache, Nginx, etc.)

## Installation

1. **Set up the database**

   - Create a new database named `banking_system` in your MySQL server
   - Import the `banking_system.sql` file to create the database schema
   - Import the `sample_data.sql` file to populate the database with sample data

2. **Configure the database connection**

   - Open the `config/db_connect.php` file
   - Update the database connection parameters if needed:
     ```php
     $host = "localhost";
     $username = "root";
     $password = "";
     $database = "banking_system";
     ```

3. **Deploy the application**

   - Copy all files to your web server's document root or a subdirectory
   - Make sure the web server has read/write permissions for the application directory

## Usage

1. **Login**

   - Access the application through your web browser
   - Use one of the sample user credentials to log in:
     - Email: `<EMAIL>`, Phone: `**************`
     - Email: `<EMAIL>`, Phone: `***********`
     - Email: `<EMAIL>`, Phone: `***********`

2. **Dashboard**

   - View your accounts and their balances
   - See recent transactions
   - Access deposit, withdrawal, and transfer functions

3. **Transactions**

   - View your complete transaction history
   - Filter transactions by account and type

4. **Transfer Funds**

   - Transfer money between your accounts
   - Transactions are processed securely with database transactions

5. **Deposit/Withdraw**

   - Deposit funds to your accounts
   - Withdraw funds from your accounts (subject to available balance)

## Security Features

- Input validation and sanitization
- Session-based authentication
- Database transaction support for financial operations
- Prepared statements to prevent SQL injection

## File Structure

- `index.php` - Login page
- `dashboard.php` - Main dashboard
- `transactions.php` - Transaction history
- `transfer.php` - Fund transfer functionality
- `deposit.php` - Deposit functionality
- `withdraw.php` - Withdrawal functionality
- `logout.php` - Logout functionality
- `config/db_connect.php` - Database connection
- `css/style.css` - Stylesheet
- `js/script.js` - JavaScript functionality
- `banking_system.sql` - Database schema
- `sample_data.sql` - Sample data

## Notes

This is a simple demonstration application and should not be used in a production environment without additional security measures and thorough testing.

## License

This project is open-source and available for educational purposes.
