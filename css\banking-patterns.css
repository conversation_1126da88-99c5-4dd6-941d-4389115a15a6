/* Banking Pattern Backgrounds */

/* This file contains CSS for creating background patterns without using actual image files */

.banking-pattern-bg {
    background-color: #f5f5f5;
    background-image: 
        radial-gradient(circle at 25px 25px, rgba(63, 81, 181, 0.15) 2px, transparent 0),
        radial-gradient(circle at 75px 75px, rgba(63, 81, 181, 0.1) 2px, transparent 0);
    background-size: 100px 100px;
}

.header-pattern-bg {
    background-color: #1a237e;
    background-image: 
        linear-gradient(30deg, rgba(255, 255, 255, 0.05) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.05) 87.5%, rgba(255, 255, 255, 0.05)),
        linear-gradient(150deg, rgba(255, 255, 255, 0.05) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.05) 87.5%, rgba(255, 255, 255, 0.05)),
        linear-gradient(30deg, rgba(255, 255, 255, 0.05) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.05) 87.5%, rgba(255, 255, 255, 0.05)),
        linear-gradient(150deg, rgba(255, 255, 255, 0.05) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.05) 87.5%, rgba(255, 255, 255, 0.05)),
        linear-gradient(60deg, rgba(255, 255, 255, 0.05) 25%, transparent 25.5%, transparent 75%, rgba(255, 255, 255, 0.05) 75%, rgba(255, 255, 255, 0.05)),
        linear-gradient(60deg, rgba(255, 255, 255, 0.05) 25%, transparent 25.5%, transparent 75%, rgba(255, 255, 255, 0.05) 75%, rgba(255, 255, 255, 0.05));
    background-size: 20px 35px;
    background-position: 0 0, 0 0, 10px 18px, 10px 18px, 0 0, 10px 18px;
}

.login-pattern-bg {
    background-color: #f5f5f5;
    background-image: 
        linear-gradient(0deg, transparent 24%, rgba(63, 81, 181, 0.05) 25%, rgba(63, 81, 181, 0.05) 26%, transparent 27%, transparent 74%, rgba(63, 81, 181, 0.05) 75%, rgba(63, 81, 181, 0.05) 76%, transparent 77%, transparent),
        linear-gradient(90deg, transparent 24%, rgba(63, 81, 181, 0.05) 25%, rgba(63, 81, 181, 0.05) 26%, transparent 27%, transparent 74%, rgba(63, 81, 181, 0.05) 75%, rgba(63, 81, 181, 0.05) 76%, transparent 77%, transparent);
    background-size: 50px 50px;
}

.dashboard-pattern-bg {
    background-color: #f5f5f5;
    background-image: 
        linear-gradient(45deg, rgba(63, 81, 181, 0.03) 25%, transparent 25%, transparent 75%, rgba(63, 81, 181, 0.03) 75%, rgba(63, 81, 181, 0.03)),
        linear-gradient(-45deg, rgba(63, 81, 181, 0.03) 25%, transparent 25%, transparent 75%, rgba(63, 81, 181, 0.03) 75%, rgba(63, 81, 181, 0.03));
    background-size: 60px 60px;
    background-position: 0 0, 30px 30px;
}

.card-pattern-bg {
    background-color: white;
    background-image: 
        radial-gradient(rgba(63, 81, 181, 0.1) 8%, transparent 8%);
    background-position: 0 0;
    background-size: 25px 25px;
}

.footer-pattern-bg {
    background-color: #1a237e;
    background-image: 
        linear-gradient(30deg, rgba(255, 255, 255, 0.05) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.05) 87.5%, rgba(255, 255, 255, 0.05)),
        linear-gradient(150deg, rgba(255, 255, 255, 0.05) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.05) 87.5%, rgba(255, 255, 255, 0.05)),
        linear-gradient(30deg, rgba(255, 255, 255, 0.05) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.05) 87.5%, rgba(255, 255, 255, 0.05)),
        linear-gradient(150deg, rgba(255, 255, 255, 0.05) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.05) 87.5%, rgba(255, 255, 255, 0.05));
    background-size: 40px 70px;
    background-position: 0 0, 0 0, 20px 35px, 20px 35px;
}
