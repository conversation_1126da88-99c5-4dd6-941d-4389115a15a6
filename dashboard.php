<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['customer_id'])) {
    header("Location: index.php");
    exit();
}

require_once 'config/db_connect.php';

// Get customer information
$customer_id = $_SESSION['customer_id'];
$customer_name = $_SESSION['customer_name'];
$customer_email = $_SESSION['customer_email'];

// Get customer details
$customer_sql = "SELECT * FROM customer WHERE CustomerID = $customer_id";
$customer_result = $conn->query($customer_sql);
$customer = $customer_result->fetch_assoc();

// Get accounts for this customer
$accounts_sql = "SELECT a.AccountID, a.AccountType, a.Balance, b.BranchName
                FROM account a
                JOIN branch b ON a.BranchID = b.BranchID
                WHERE a.CustomerID = $customer_id";
$accounts_result = $conn->query($accounts_sql);

// Calculate total balance across all accounts
$total_balance = 0;
$total_accounts = 0;
$accounts_data = array();

if ($accounts_result->num_rows > 0) {
    while($account = $accounts_result->fetch_assoc()) {
        $total_balance += $account['Balance'];
        $total_accounts++;
        $accounts_data[] = $account;
    }
    // Reset the result pointer
    $accounts_result->data_seek(0);
}

// Get recent transactions
$transactions_sql = "SELECT t.TransactionID, t.Amount, t.Date, t.Type, a.AccountID, a.AccountType
                    FROM transaction t
                    JOIN account a ON t.AccountID = a.AccountID
                    WHERE a.CustomerID = $customer_id
                    ORDER BY t.Date DESC LIMIT 5";
$transactions_result = $conn->query($transactions_sql);

// Get transaction statistics
$transaction_stats_sql = "SELECT t.Type, COUNT(*) as count, SUM(t.Amount) as total
                         FROM transaction t
                         JOIN account a ON t.AccountID = a.AccountID
                         WHERE a.CustomerID = $customer_id
                         GROUP BY t.Type";
$transaction_stats_result = $conn->query($transaction_stats_sql);

$transaction_stats = array(
    'Credit' => array('count' => 0, 'total' => 0),
    'Debit' => array('count' => 0, 'total' => 0),
    'Transfer' => array('count' => 0, 'total' => 0)
);

if ($transaction_stats_result->num_rows > 0) {
    while($stat = $transaction_stats_result->fetch_assoc()) {
        $transaction_stats[$stat['Type']]['count'] = $stat['count'];
        $transaction_stats[$stat['Type']]['total'] = $stat['total'];
    }
}

// Get pending account requests
$pending_requests_sql = "SELECT COUNT(*) as count FROM pending_account WHERE CustomerID = $customer_id AND Status = 'pending'";
$pending_requests_result = $conn->query($pending_requests_sql);
$pending_requests = 0;

if ($pending_requests_result->num_rows > 0) {
    $row = $pending_requests_result->fetch_assoc();
    $pending_requests = $row['count'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Dashboard</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/landing.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/enhanced-style.css">
    <link rel="stylesheet" href="css/banking-patterns.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Banking System</h1>
            <div class="user-info">
                <p>Welcome, <?php echo htmlspecialchars($customer_name); ?></p>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </header>

        <nav>
            <ul>
                <li><a href="dashboard.php" class="active">Dashboard</a></li>
                <li><a href="transactions.php">Transactions</a></li>
                <li><a href="transfer.php">Transfer Funds</a></li>
                <li><a href="account_request.php">Request New Account</a></li>
                <li><a href="profile.php">My Profile</a></li>
            </ul>
        </nav>

        <main>
            <!-- Welcome Section -->
            <section class="welcome-card">
                <h2>Welcome back, <?php echo htmlspecialchars($customer_name); ?>!</h2>
                <p>Here's an overview of your banking activity. Manage your accounts, view transactions, and more.</p>
            </section>

            <!-- Dashboard Overview -->
            <section class="dashboard-overview">
                <div class="summary-card">
                    <h3>Total Balance</h3>
                    <div class="summary-value">$<?php echo number_format($total_balance, 2); ?></div>
                    <div class="summary-label">Across all accounts</div>
                </div>

                <div class="summary-card">
                    <h3>Active Accounts</h3>
                    <div class="summary-value"><?php echo $total_accounts; ?></div>
                    <div class="summary-label">Accounts in your name</div>
                </div>

                <div class="summary-card">
                    <h3>Recent Credits</h3>
                    <div class="summary-value credit">$<?php echo number_format($transaction_stats['Credit']['total'], 2); ?></div>
                    <div class="summary-label"><?php echo $transaction_stats['Credit']['count']; ?> transactions</div>
                </div>

                <div class="summary-card">
                    <h3>Recent Debits</h3>
                    <div class="summary-value debit">$<?php echo number_format($transaction_stats['Debit']['total'], 2); ?></div>
                    <div class="summary-label"><?php echo $transaction_stats['Debit']['count']; ?> transactions</div>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="quick-actions">
                <h2>Quick Actions</h2>
                <div class="actions-grid">
                    <a href="transfer.php" class="action-card">
                        <div class="action-icon"><i class="fas fa-exchange-alt"></i></div>
                        <h3>Transfer Funds</h3>
                        <p>Move money between accounts</p>
                    </a>

                    <a href="transactions.php" class="action-card">
                        <div class="action-icon"><i class="fas fa-history"></i></div>
                        <h3>Transaction History</h3>
                        <p>View all your transactions</p>
                    </a>

                    <a href="account_request.php" class="action-card">
                        <div class="action-icon"><i class="fas fa-plus-circle"></i></div>
                        <h3>New Account</h3>
                        <p>Request a new account</p>
                    </a>

                    <a href="profile.php" class="action-card">
                        <div class="action-icon"><i class="fas fa-user-cog"></i></div>
                        <h3>Profile Settings</h3>
                        <p>Update your information</p>
                    </a>
                </div>
            </section>

            <!-- Accounts Section -->
            <section class="accounts-section">
                <h2>Your Accounts</h2>

                <?php if ($accounts_result->num_rows > 0): ?>
                    <div class="accounts-container">
                        <?php while($account = $accounts_result->fetch_assoc()): ?>
                            <div class="account-card">
                                <h3><?php echo htmlspecialchars($account['AccountType']); ?> Account</h3>
                                <p class="account-number">Account #: <?php echo htmlspecialchars($account['AccountID']); ?></p>
                                <p class="branch">Branch: <?php echo htmlspecialchars($account['BranchName']); ?></p>
                                <p class="balance">$<?php echo number_format($account['Balance'], 2); ?></p>
                                <p class="account-status">
                                    <span class="status-badge status-active">Active</span>
                                </p>
                                <div class="account-actions">
                                    <a href="deposit.php?account=<?php echo $account['AccountID']; ?>" class="btn">Deposit</a>
                                    <a href="withdraw.php?account=<?php echo $account['AccountID']; ?>" class="btn">Withdraw</a>
                                    <a href="transactions.php?account=<?php echo $account['AccountID']; ?>" class="btn">Transactions</a>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>
                <?php else: ?>
                    <div class="info-message">
                        <p>No accounts found. <a href="account_request.php">Request a new account</a> to get started.</p>
                    </div>
                <?php endif; ?>

                <?php if ($pending_requests > 0): ?>
                    <div class="info-message" style="margin-top: 15px;">
                        <p>You have <?php echo $pending_requests; ?> pending account request(s). <a href="account_request.php">View details</a>.</p>
                    </div>
                <?php endif; ?>
            </section>

            <!-- Transactions Section -->
            <section class="transactions-section">
                <h2>Recent Transactions</h2>

                <?php if ($transactions_result && $transactions_result->num_rows > 0): ?>
                    <table class="transactions-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Account</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($transaction = $transactions_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $transaction['TransactionID']; ?></td>
                                    <td>
                                        <a href="transactions.php?account=<?php echo $transaction['AccountID']; ?>">
                                            <?php echo htmlspecialchars($transaction['AccountType']); ?>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="<?php echo strtolower($transaction['Type']); ?>">
                                            <?php echo htmlspecialchars($transaction['Type']); ?>
                                        </span>
                                    </td>
                                    <td class="<?php echo strtolower($transaction['Type']); ?>">
                                        $<?php echo number_format($transaction['Amount'], 2); ?>
                                    </td>
                                    <td><?php echo date('M d, Y', strtotime($transaction['Date'])); ?></td>
                                    <td>
                                        <a href="transaction_details.php?id=<?php echo $transaction['TransactionID']; ?>" class="btn btn-sm">Details</a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                    <a href="transactions.php" class="view-all">View All Transactions</a>
                <?php else: ?>
                    <p>No recent transactions found.</p>
                <?php endif; ?>
            </section>
        </main>

        <footer>
            <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
        </footer>
    </div>

    <script src="js/script.js"></script>
</body>
</html>

<?php
$conn->close();
?>
