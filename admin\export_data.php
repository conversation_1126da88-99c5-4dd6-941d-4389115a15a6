<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

require_once '../config/db_connect.php';

// Get request parameters
$export_type = isset($_GET['type']) ? $_GET['type'] : '';
$time_period = isset($_GET['period']) ? $_GET['period'] : 'monthly';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// Function to get date format based on time period
function getDateFormat($period) {
    switch ($period) {
        case 'daily':
            return '%Y-%m-%d';
        case 'weekly':
            return '%x (Week %v)';
        case 'monthly':
            return '%Y-%m';
        case 'yearly':
            return '%Y';
        default:
            return '%Y-%m-%d';
    }
}

// Function to get group by clause based on time period
function getGroupByClause($period) {
    switch ($period) {
        case 'daily':
            return "DATE(Date)";
        case 'weekly':
            return "YEARWEEK(Date, 1)";
        case 'monthly':
            return "YEAR(Date), MONTH(Date)";
        case 'yearly':
            return "YEAR(Date)";
        default:
            return "DATE(Date)";
    }
}

// Set headers for Excel download
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment; filename="' . $export_type . '_' . date('Y-m-d') . '.xls"');
header('Pragma: no-cache');
header('Expires: 0');

// Start output buffering
ob_start();

// Output Excel XML header
echo '<?xml version="1.0" encoding="UTF-8"?>';
echo '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">';
echo '<Worksheet ss:Name="Sheet1">';
echo '<Table>';

// Process different export types
switch ($export_type) {
    case 'transaction_trends':
        // Get transaction trends over time
        $date_format = getDateFormat($time_period);
        $group_by = getGroupByClause($time_period);
        
        $sql = "SELECT 
                    DATE_FORMAT(Date, '$date_format') as period,
                    SUM(CASE WHEN Type = 'Credit' THEN Amount ELSE 0 END) as credit_amount,
                    SUM(CASE WHEN Type = 'Debit' THEN Amount ELSE 0 END) as debit_amount,
                    SUM(CASE WHEN Type = 'Transfer' THEN Amount ELSE 0 END) as transfer_amount,
                    COUNT(*) as transaction_count
                FROM transaction
                WHERE Date BETWEEN '$start_date' AND '$end_date'
                GROUP BY $group_by
                ORDER BY Date ASC";
        
        $result = $conn->query($sql);
        
        // Output header row
        echo '<Row>';
        echo '<Cell><Data ss:Type="String">Period</Data></Cell>';
        echo '<Cell><Data ss:Type="String">Credits ($)</Data></Cell>';
        echo '<Cell><Data ss:Type="String">Debits ($)</Data></Cell>';
        echo '<Cell><Data ss:Type="String">Transfers ($)</Data></Cell>';
        echo '<Cell><Data ss:Type="String">Transaction Count</Data></Cell>';
        echo '</Row>';
        
        // Output data rows
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                echo '<Row>';
                echo '<Cell><Data ss:Type="String">' . $row['period'] . '</Data></Cell>';
                echo '<Cell><Data ss:Type="Number">' . $row['credit_amount'] . '</Data></Cell>';
                echo '<Cell><Data ss:Type="Number">' . $row['debit_amount'] . '</Data></Cell>';
                echo '<Cell><Data ss:Type="Number">' . $row['transfer_amount'] . '</Data></Cell>';
                echo '<Cell><Data ss:Type="Number">' . $row['transaction_count'] . '</Data></Cell>';
                echo '</Row>';
            }
        }
        break;
        
    case 'transaction_types':
        // Get distribution of transaction types
        $sql = "SELECT 
                    Type,
                    COUNT(*) as count,
                    SUM(Amount) as total_amount
                FROM transaction
                WHERE Date BETWEEN '$start_date' AND '$end_date'
                GROUP BY Type";
        
        $result = $conn->query($sql);
        
        // Output header row
        echo '<Row>';
        echo '<Cell><Data ss:Type="String">Transaction Type</Data></Cell>';
        echo '<Cell><Data ss:Type="String">Count</Data></Cell>';
        echo '<Cell><Data ss:Type="String">Total Amount ($)</Data></Cell>';
        echo '<Cell><Data ss:Type="String">Percentage</Data></Cell>';
        echo '</Row>';
        
        // Calculate total count for percentage
        $total_count = 0;
        $rows = [];
        
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $total_count += $row['count'];
                $rows[] = $row;
            }
            
            // Output data rows
            foreach ($rows as $row) {
                $percentage = round(($row['count'] / $total_count) * 100, 2);
                echo '<Row>';
                echo '<Cell><Data ss:Type="String">' . $row['Type'] . '</Data></Cell>';
                echo '<Cell><Data ss:Type="Number">' . $row['count'] . '</Data></Cell>';
                echo '<Cell><Data ss:Type="Number">' . $row['total_amount'] . '</Data></Cell>';
                echo '<Cell><Data ss:Type="Number">' . $percentage . '</Data></Cell>';
                echo '</Row>';
            }
        }
        break;
        
    case 'account_types':
        // Get transaction volumes by account type
        $sql = "SELECT 
                    a.AccountType,
                    COUNT(t.TransactionID) as transaction_count,
                    SUM(t.Amount) as total_amount
                FROM transaction t
                JOIN account a ON t.AccountID = a.AccountID
                WHERE t.Date BETWEEN '$start_date' AND '$end_date'
                GROUP BY a.AccountType
                ORDER BY transaction_count DESC";
        
        $result = $conn->query($sql);
        
        // Output header row
        echo '<Row>';
        echo '<Cell><Data ss:Type="String">Account Type</Data></Cell>';
        echo '<Cell><Data ss:Type="String">Transaction Count</Data></Cell>';
        echo '<Cell><Data ss:Type="String">Total Amount ($)</Data></Cell>';
        echo '</Row>';
        
        // Output data rows
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                echo '<Row>';
                echo '<Cell><Data ss:Type="String">' . $row['AccountType'] . '</Data></Cell>';
                echo '<Cell><Data ss:Type="Number">' . $row['transaction_count'] . '</Data></Cell>';
                echo '<Cell><Data ss:Type="Number">' . $row['total_amount'] . '</Data></Cell>';
                echo '</Row>';
            }
        }
        break;
        
    default:
        echo '<Row><Cell><Data ss:Type="String">Invalid export type</Data></Cell></Row>';
}

// Close Excel XML
echo '</Table>';
echo '</Worksheet>';
echo '</Workbook>';

// End output buffering and send to browser
ob_end_flush();

// Close database connection
$conn->close();
?>
