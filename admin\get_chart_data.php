<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

require_once '../config/db_connect.php';

// Get request parameters
$chart_type = isset($_GET['chart']) ? $_GET['chart'] : '';
$time_period = isset($_GET['period']) ? $_GET['period'] : 'monthly';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// Initialize response array
$response = [
    'success' => true,
    'data' => [],
    'labels' => [],
    'datasets' => []
];

// Function to get date format based on time period
function getDateFormat($period) {
    switch ($period) {
        case 'daily':
            return '%Y-%m-%d';
        case 'weekly':
            return '%x (Week %v)';
        case 'monthly':
            return '%Y-%m';
        case 'yearly':
            return '%Y';
        default:
            return '%Y-%m-%d';
    }
}

// Function to get group by clause based on time period
function getGroupByClause($period) {
    switch ($period) {
        case 'daily':
            return "DATE(Date)";
        case 'weekly':
            return "YEARWEEK(Date, 1)";
        case 'monthly':
            return "YEAR(Date), MONTH(Date)";
        case 'yearly':
            return "YEAR(Date)";
        default:
            return "DATE(Date)";
    }
}

// Function to get date format for labels
function getDateFormatForLabels($period) {
    switch ($period) {
        case 'daily':
            return 'M d, Y';
        case 'weekly':
            return '\W\e\e\k W, Y';
        case 'monthly':
            return 'M Y';
        case 'yearly':
            return 'Y';
        default:
            return 'M d, Y';
    }
}

// Process different chart types
switch ($chart_type) {
    case 'transaction_trends':
        // Get transaction trends over time
        $date_format = getDateFormat($time_period);
        $group_by = getGroupByClause($time_period);
        
        $sql = "SELECT 
                    DATE_FORMAT(Date, '$date_format') as period,
                    SUM(CASE WHEN Type = 'Credit' THEN Amount ELSE 0 END) as credit_amount,
                    SUM(CASE WHEN Type = 'Debit' THEN Amount ELSE 0 END) as debit_amount,
                    SUM(CASE WHEN Type = 'Transfer' THEN Amount ELSE 0 END) as transfer_amount,
                    COUNT(*) as transaction_count
                FROM transaction
                WHERE Date BETWEEN '$start_date' AND '$end_date'
                GROUP BY $group_by
                ORDER BY Date ASC";
        
        $result = $conn->query($sql);
        
        $labels = [];
        $credit_data = [];
        $debit_data = [];
        $transfer_data = [];
        $count_data = [];
        
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $labels[] = $row['period'];
                $credit_data[] = $row['credit_amount'];
                $debit_data[] = $row['debit_amount'];
                $transfer_data[] = $row['transfer_amount'];
                $count_data[] = $row['transaction_count'];
            }
        }
        
        $response['labels'] = $labels;
        $response['datasets'] = [
            [
                'label' => 'Credits',
                'data' => $credit_data,
                'backgroundColor' => 'rgba(76, 175, 80, 0.2)',
                'borderColor' => 'rgba(76, 175, 80, 1)',
                'borderWidth' => 1
            ],
            [
                'label' => 'Debits',
                'data' => $debit_data,
                'backgroundColor' => 'rgba(244, 67, 54, 0.2)',
                'borderColor' => 'rgba(244, 67, 54, 1)',
                'borderWidth' => 1
            ],
            [
                'label' => 'Transfers',
                'data' => $transfer_data,
                'backgroundColor' => 'rgba(33, 150, 243, 0.2)',
                'borderColor' => 'rgba(33, 150, 243, 1)',
                'borderWidth' => 1
            ]
        ];
        break;
        
    case 'transaction_types':
        // Get distribution of transaction types
        $sql = "SELECT 
                    Type,
                    COUNT(*) as count,
                    SUM(Amount) as total_amount
                FROM transaction
                WHERE Date BETWEEN '$start_date' AND '$end_date'
                GROUP BY Type";
        
        $result = $conn->query($sql);
        
        $labels = [];
        $count_data = [];
        $amount_data = [];
        $background_colors = [
            'Credit' => 'rgba(76, 175, 80, 0.7)',
            'Debit' => 'rgba(244, 67, 54, 0.7)',
            'Transfer' => 'rgba(33, 150, 243, 0.7)'
        ];
        $colors = [];
        
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $labels[] = $row['Type'];
                $count_data[] = $row['count'];
                $amount_data[] = $row['total_amount'];
                $colors[] = $background_colors[$row['Type']] ?? 'rgba(156, 39, 176, 0.7)';
            }
        }
        
        $response['labels'] = $labels;
        $response['datasets'] = [
            [
                'data' => $count_data,
                'backgroundColor' => $colors,
                'borderWidth' => 1
            ]
        ];
        $response['amounts'] = $amount_data;
        break;
        
    case 'account_types':
        // Get transaction volumes by account type
        $sql = "SELECT 
                    a.AccountType,
                    COUNT(t.TransactionID) as transaction_count,
                    SUM(t.Amount) as total_amount
                FROM transaction t
                JOIN account a ON t.AccountID = a.AccountID
                WHERE t.Date BETWEEN '$start_date' AND '$end_date'
                GROUP BY a.AccountType
                ORDER BY transaction_count DESC";
        
        $result = $conn->query($sql);
        
        $labels = [];
        $count_data = [];
        $amount_data = [];
        
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $labels[] = $row['AccountType'];
                $count_data[] = $row['transaction_count'];
                $amount_data[] = $row['total_amount'];
            }
        }
        
        $response['labels'] = $labels;
        $response['datasets'] = [
            [
                'label' => 'Transaction Count',
                'data' => $count_data,
                'backgroundColor' => 'rgba(156, 39, 176, 0.7)',
                'borderColor' => 'rgba(156, 39, 176, 1)',
                'borderWidth' => 1
            ]
        ];
        $response['amounts'] = $amount_data;
        break;
        
    default:
        $response['success'] = false;
        $response['error'] = 'Invalid chart type';
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);

// Close database connection
$conn->close();
?>
