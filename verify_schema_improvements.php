<?php
/**
 * Verify Schema Improvements Implementation
 */

require_once 'config/db_connect.php';

echo "<h1>Schema Improvements Verification</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .check-item { margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }
    .check-pass { border-left-color: green; background-color: #f0fff0; }
    .check-fail { border-left-color: red; background-color: #fff0f0; }
</style>";

echo "<p class='info'>Verifying schema improvements for banking_system_new</p>";

$checks = [];

// Check 1: Admin Roles Table
echo "<h2>1. Admin Roles System</h2>";
$result = $conn->query("SHOW TABLES LIKE 'admin_roles'");
if ($result->num_rows > 0) {
    echo "<div class='check-item check-pass'>";
    echo "<p class='success'>✅ admin_roles table exists</p>";
    
    // Check roles data
    $roles_result = $conn->query("SELECT * FROM admin_roles");
    echo "<p>Roles found: " . $roles_result->num_rows . "</p>";
    
    if ($roles_result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>Role ID</th><th>Role Name</th><th>Description</th></tr>";
        while ($row = $roles_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['RoleID'] . "</td>";
            echo "<td>" . $row['RoleName'] . "</td>";
            echo "<td>" . $row['Description'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    $checks['admin_roles'] = true;
} else {
    echo "<div class='check-item check-fail'>";
    echo "<p class='error'>❌ admin_roles table does not exist</p>";
    echo "</div>";
    $checks['admin_roles'] = false;
}

// Check 2: Admin table RoleID column
echo "<h2>2. Admin Table Role Integration</h2>";
$result = $conn->query("SHOW COLUMNS FROM admin LIKE 'RoleID'");
if ($result->num_rows > 0) {
    echo "<div class='check-item check-pass'>";
    echo "<p class='success'>✅ RoleID column exists in admin table</p>";
    
    // Check admin users with roles
    $admin_result = $conn->query("SELECT a.AdminID, a.Username, a.Name, r.RoleName 
                                  FROM admin a 
                                  LEFT JOIN admin_roles r ON a.RoleID = r.RoleID");
    if ($admin_result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>Admin ID</th><th>Username</th><th>Name</th><th>Role</th></tr>";
        while ($row = $admin_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['AdminID'] . "</td>";
            echo "<td>" . $row['Username'] . "</td>";
            echo "<td>" . $row['Name'] . "</td>";
            echo "<td>" . ($row['RoleName'] ?? 'No Role') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    $checks['admin_role_integration'] = true;
} else {
    echo "<div class='check-item check-fail'>";
    echo "<p class='error'>❌ RoleID column does not exist in admin table</p>";
    echo "</div>";
    $checks['admin_role_integration'] = false;
}

// Check 3: Admin Actions Audit Table
echo "<h2>3. Admin Actions Audit Log</h2>";
$result = $conn->query("SHOW TABLES LIKE 'admin_actions'");
if ($result->num_rows > 0) {
    echo "<div class='check-item check-pass'>";
    echo "<p class='success'>✅ admin_actions table exists</p>";
    
    // Show table structure
    $structure = $conn->query("DESCRIBE admin_actions");
    echo "<table>";
    echo "<tr><th>Field</th><th>Type</th><th>Key</th></tr>";
    while ($row = $structure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    $checks['admin_actions'] = true;
} else {
    echo "<div class='check-item check-fail'>";
    echo "<p class='error'>❌ admin_actions table does not exist</p>";
    echo "</div>";
    $checks['admin_actions'] = false;
}

// Check 4: Enhanced Pending Account Table
echo "<h2>4. Enhanced Pending Account Table</h2>";
$pending_columns = ['ApprovedBy', 'ApprovalDate', 'ApprovalNotes', 'RejectedBy', 'RejectionDate', 'RejectionReason'];
$pending_checks = [];

foreach ($pending_columns as $column) {
    $result = $conn->query("SHOW COLUMNS FROM pending_account LIKE '$column'");
    $pending_checks[$column] = $result->num_rows > 0;
}

$pending_pass = array_sum($pending_checks) == count($pending_columns);
echo "<div class='check-item " . ($pending_pass ? 'check-pass' : 'check-fail') . "'>";
if ($pending_pass) {
    echo "<p class='success'>✅ All pending account enhancement columns exist</p>";
} else {
    echo "<p class='error'>❌ Some pending account enhancement columns are missing</p>";
}

echo "<ul>";
foreach ($pending_checks as $column => $exists) {
    echo "<li>" . ($exists ? '✅' : '❌') . " $column</li>";
}
echo "</ul>";
echo "</div>";
$checks['pending_account_enhanced'] = $pending_pass;

// Check 5: Enhanced Account Table
echo "<h2>5. Enhanced Account Table</h2>";
$account_columns = ['CreatedBy', 'CreatedDate', 'LastModifiedBy', 'LastModifiedDate', 'Status'];
$account_checks = [];

foreach ($account_columns as $column) {
    $result = $conn->query("SHOW COLUMNS FROM account LIKE '$column'");
    $account_checks[$column] = $result->num_rows > 0;
}

$account_pass = array_sum($account_checks) == count($account_columns);
echo "<div class='check-item " . ($account_pass ? 'check-pass' : 'check-fail') . "'>";
if ($account_pass) {
    echo "<p class='success'>✅ All account enhancement columns exist</p>";
} else {
    echo "<p class='error'>❌ Some account enhancement columns are missing</p>";
}

echo "<ul>";
foreach ($account_checks as $column => $exists) {
    echo "<li>" . ($exists ? '✅' : '❌') . " $column</li>";
}
echo "</ul>";
echo "</div>";
$checks['account_enhanced'] = $account_pass;

// Check 6: Enhanced Transaction Table
echo "<h2>6. Enhanced Transaction Table</h2>";
$transaction_columns = ['ProcessedBy', 'RequiresApproval', 'ApprovedBy', 'ApprovalDate', 'TransactionStatus'];
$transaction_checks = [];

foreach ($transaction_columns as $column) {
    $result = $conn->query("SHOW COLUMNS FROM transaction LIKE '$column'");
    $transaction_checks[$column] = $result->num_rows > 0;
}

$transaction_pass = array_sum($transaction_checks) == count($transaction_columns);
echo "<div class='check-item " . ($transaction_pass ? 'check-pass' : 'check-fail') . "'>";
if ($transaction_pass) {
    echo "<p class='success'>✅ All transaction enhancement columns exist</p>";
} else {
    echo "<p class='error'>❌ Some transaction enhancement columns are missing</p>";
}

echo "<ul>";
foreach ($transaction_checks as $column => $exists) {
    echo "<li>" . ($exists ? '✅' : '❌') . " $column</li>";
}
echo "</ul>";
echo "</div>";
$checks['transaction_enhanced'] = $transaction_pass;

// Check 7: Foreign Key Relationships
echo "<h2>7. Foreign Key Relationships</h2>";
$fk_query = "
    SELECT 
        TABLE_NAME,
        COLUMN_NAME,
        CONSTRAINT_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
    FROM 
        INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
    WHERE 
        REFERENCED_TABLE_SCHEMA = 'banking_system_new'
        AND REFERENCED_TABLE_NAME IS NOT NULL
        AND (REFERENCED_TABLE_NAME = 'admin' OR TABLE_NAME LIKE '%admin%')
";

$fk_result = $conn->query($fk_query);
echo "<div class='check-item check-pass'>";
echo "<p class='success'>✅ Admin-related foreign key relationships:</p>";

if ($fk_result->num_rows > 0) {
    echo "<table>";
    echo "<tr><th>Table</th><th>Column</th><th>References</th><th>Referenced Column</th></tr>";
    
    while ($row = $fk_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['TABLE_NAME'] . "</td>";
        echo "<td>" . $row['COLUMN_NAME'] . "</td>";
        echo "<td>" . $row['REFERENCED_TABLE_NAME'] . "</td>";
        echo "<td>" . $row['REFERENCED_COLUMN_NAME'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='warning'>⚠️ No admin-related foreign key relationships found</p>";
}
echo "</div>";

// Overall Summary
echo "<h2>Overall Verification Summary</h2>";
$total_checks = count($checks);
$passed_checks = array_sum($checks);

echo "<div class='check-item " . ($passed_checks == $total_checks ? 'check-pass' : 'check-fail') . "'>";
echo "<h3>Results: $passed_checks / $total_checks checks passed</h3>";

if ($passed_checks == $total_checks) {
    echo "<p class='success'>🎉 All schema improvements have been successfully implemented!</p>";
    echo "<p>The admin table is now properly integrated with the banking system with:</p>";
    echo "<ul>";
    echo "<li>Role-based access control</li>";
    echo "<li>Audit logging for all admin actions</li>";
    echo "<li>Admin approval workflow for accounts</li>";
    echo "<li>Transaction oversight and approval</li>";
    echo "<li>Proper foreign key relationships</li>";
    echo "</ul>";
} else {
    echo "<p class='error'>⚠️ Some improvements are missing. Please run the implementation script again.</p>";
}
echo "</div>";

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li><a href='admin/login.php'>Test Enhanced Admin Login</a></li>";
echo "<li><a href='create_admin_dashboard_features.php'>Create Admin Dashboard Features</a></li>";
echo "<li><a href='analyze_schema.php'>Re-analyze Complete Schema</a></li>";
echo "<li><a href='index.php'>Return to Banking System</a></li>";
echo "</ol>";

$conn->close();
?>
