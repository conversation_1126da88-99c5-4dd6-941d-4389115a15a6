# Email Verification System for Banking Application

This document provides instructions on how to set up and use the email verification system for the banking application.

## Overview

The email verification system ensures that users provide valid email addresses during registration by sending a verification link to the provided email. The user must click the link to verify their email address before they can log in to the system.

## Features

1. **Secure Token Generation**: Generates a secure, unique verification token for each new user registration
2. **Email Sending**: Uses PHPMailer to send professional HTML emails with verification links
3. **Token Expiry**: Tokens expire after 24 hours for security
4. **Resend Functionality**: Users can request a new verification email if needed
5. **Logging**: All verification attempts are logged for security purposes
6. **Error Handling**: Proper error handling for failed email delivery attempts
7. **User Feedback**: Clear user feedback throughout the verification process

## Setup Instructions

### 1. Database Setup

Run the database update script to add the necessary columns and tables:

```
php update_db.php
```

This will:
- Add a `VerificationExpiry` column to the `customer` table
- Create an `email_log` table for tracking email activities
- Create a `verification_log` table for tracking verification activities

### 2. P<PERSON><PERSON>ailer Installation

For production use, install PHPMailer using Composer:

```
composer require phpmailer/phpmailer
```

For development, a mock PHPMailer class is provided in the `vendor/autoload.php` file.

### 3. Email Configuration

Update the email configuration in `includes/email_service.php`:

```php
// Email configuration constants
define('MAIL_HOST', 'your-smtp-server.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '<EMAIL>');
define('MAIL_PASSWORD', 'your-email-password');
define('MAIL_FROM_EMAIL', '<EMAIL>');
define('MAIL_FROM_NAME', 'Your Bank Name');
define('MAIL_REPLY_TO', '<EMAIL>');
```

### 4. Development Mode

For testing purposes, the system includes a development mode that displays verification links directly on the page instead of sending emails. This mode is enabled by default in the following files:

- `signup.php`
- `verify.php`
- `verify_resend.php`

To disable development mode for production, set `DEVELOPMENT_MODE` to `false` in these files:

```php
// Set to false in production
define('DEVELOPMENT_MODE', false);
```

## How It Works

### Registration Process

1. User submits the registration form
2. System validates the form data
3. System generates a secure verification token
4. System stores the token and its expiry time in the database
5. System sends a verification email to the user
6. User receives a success message with instructions

### Verification Process

1. User clicks the verification link in the email
2. System validates the token
3. If valid, system marks the email as verified
4. User receives a success message
5. User can now log in to the system

### Resend Verification Email

1. User clicks the "Resend Verification Email" link
2. User enters their email address
3. System generates a new verification token
4. System sends a new verification email
5. User receives a success message

## Logs

The system maintains two log files:

1. `logs/email_log.txt`: Records all email sending attempts
2. `logs/verification_log.txt`: Records all verification activities

These logs can be used for debugging and security auditing.

## Customization

### Email Templates

The email templates can be customized in `includes/email_service.php`:

- `get_verification_email_html_template()`: HTML email template
- `get_verification_email_text_template()`: Plain text email template

### Token Settings

Token settings can be adjusted in `includes/token_service.php`:

- Token length: Modify the `$length` parameter in `generate_token()`
- Token expiry: Modify the `$expiry_hours` parameter in `store_verification_token()`

## Troubleshooting

### Emails Not Sending

1. Check the SMTP settings in `includes/email_service.php`
2. Verify that your SMTP server allows sending from your application
3. Check the `logs/email_log.txt` file for error messages

### Verification Links Not Working

1. Ensure the token is being properly stored in the database
2. Check if the token has expired (default: 24 hours)
3. Verify that the base URL is correctly determined in `get_base_url()`
4. Check the `logs/verification_log.txt` file for error messages

## Security Considerations

1. Tokens are generated using cryptographically secure functions
2. Tokens expire after 24 hours
3. All verification attempts are logged
4. Prepared statements are used to prevent SQL injection
5. Input validation is performed on all user inputs
