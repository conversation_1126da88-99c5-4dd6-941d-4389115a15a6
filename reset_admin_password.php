<?php
// Output all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Reset Admin Password</h1>";

// Database connection parameters
$host = "localhost";
$username = "root";
$password = "";
$database = "banking_system_new";

// Create connection
$conn = new mysqli($host, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    die("<p>Connection failed: " . $conn->connect_error . "</p>");
} else {
    echo "<p>Successfully connected to $database database.</p>";
}

// Check if admin table exists
$result = $conn->query("SHOW TABLES LIKE 'admin'");
if ($result->num_rows == 0) {
    echo "<p>Admin table does not exist. Creating it now...</p>";
    
    // Create admin table
    $sql = "CREATE TABLE IF NOT EXISTS admin (
      AdminID int(11) NOT NULL AUTO_INCREMENT,
      Username varchar(50) NOT NULL,
      Password varchar(255) NOT NULL,
      Name varchar(100) NOT NULL,
      Email varchar(100) NOT NULL,
      LastLogin datetime DEFAULT NULL,
      PRIMARY KEY (AdminID),
      UNIQUE KEY Username (Username)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    if (!$conn->query($sql)) {
        echo "<p>Error creating admin table: " . $conn->error . "</p>";
        exit;
    } else {
        echo "<p>Admin table created successfully.</p>";
    }
}

// Check if admin user exists
$result = $conn->query("SELECT * FROM admin WHERE Username = 'admin'");
if ($result->num_rows > 0) {
    echo "<p>Admin user exists. Updating password...</p>";
    
    // Generate password hash
    $new_password = 'admin123';
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    
    // Update admin password
    $sql = "UPDATE admin SET Password = '$hashed_password' WHERE Username = 'admin'";
    
    if ($conn->query($sql)) {
        echo "<p>Admin password updated successfully.</p>";
    } else {
        echo "<p>Error updating admin password: " . $conn->error . "</p>";
    }
} else {
    echo "<p>Admin user does not exist. Creating admin user...</p>";
    
    // Generate password hash
    $new_password = 'admin123';
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    
    // Insert admin user
    $sql = "INSERT INTO admin (Username, Password, Name, Email) VALUES 
            ('admin', '$hashed_password', 'System Administrator', '<EMAIL>')";
    
    if ($conn->query($sql)) {
        echo "<p>Admin user created successfully.</p>";
    } else {
        echo "<p>Error creating admin user: " . $conn->error . "</p>";
    }
}

// Display admin credentials
echo "<h2>Admin Credentials</h2>";
echo "<p>Username: admin</p>";
echo "<p>Password: admin123</p>";

// Close connection
$conn->close();

echo "<p><a href='admin/login.php'>Go to Admin Login</a></p>";
?>
