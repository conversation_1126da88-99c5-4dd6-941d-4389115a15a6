<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config/db_connect.php';

// Get admin information
$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];

// Get counts for dashboard
$pending_customers_count = 0;
$pending_accounts_count = 0;
$total_customers_count = 0;
$total_accounts_count = 0;

// Count pending customers
$pending_customers_sql = "SELECT COUNT(*) as count FROM customer WHERE Status = 'pending' AND IsVerified = 1";
$result = $conn->query($pending_customers_sql);
if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $pending_customers_count = $row['count'];
}

// Count pending account requests
$pending_accounts_sql = "SELECT COUNT(*) as count FROM pending_account WHERE Status = 'pending'";
$result = $conn->query($pending_accounts_sql);
if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $pending_accounts_count = $row['count'];
}

// Count total customers
$total_customers_sql = "SELECT COUNT(*) as count FROM customer";
$result = $conn->query($total_customers_sql);
if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $total_customers_count = $row['count'];
}

// Count total accounts
$total_accounts_sql = "SELECT COUNT(*) as count FROM account";
$result = $conn->query($total_accounts_sql);
if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $total_accounts_count = $row['count'];
}

// Get recent transactions
$recent_transactions_sql = "SELECT t.TransactionID, t.Amount, t.Date, t.Type, a.AccountID, c.Name
                           FROM transaction t
                           JOIN account a ON t.AccountID = a.AccountID
                           JOIN customer c ON a.CustomerID = c.CustomerID
                           ORDER BY t.Date DESC LIMIT 5";
$recent_transactions_result = $conn->query($recent_transactions_sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Admin Dashboard</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="../css/enhanced-style.css">
    <link rel="stylesheet" href="../css/banking-patterns.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <header class="admin-header">
            <h1>Banking System Admin</h1>
            <div class="user-info">
                <p>Welcome, <?php echo htmlspecialchars($admin_name); ?></p>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </header>

        <nav class="admin-nav">
            <ul>
                <li><a href="dashboard.php" class="active">Dashboard</a></li>
                <li><a href="customers.php">Manage Customers</a></li>
                <li><a href="accounts.php">Manage Accounts</a></li>
                <li><a href="transactions.php">Transactions</a></li>
                <li><a href="reports.php">Reports</a></li>
            </ul>
        </nav>

        <main class="admin-main">
            <h2>Admin Dashboard</h2>

            <section class="dashboard-stats">
                <div class="stat-card">
                    <h3>Pending Approvals</h3>
                    <div class="stat-value"><?php echo $pending_customers_count; ?></div>
                    <p>New Customers</p>
                    <a href="customers.php?filter=pending" class="btn btn-sm">View All</a>
                </div>

                <div class="stat-card">
                    <h3>Account Requests</h3>
                    <div class="stat-value"><?php echo $pending_accounts_count; ?></div>
                    <p>Pending Accounts</p>
                    <a href="accounts.php?filter=pending" class="btn btn-sm">View All</a>
                </div>

                <div class="stat-card">
                    <h3>Total Customers</h3>
                    <div class="stat-value"><?php echo $total_customers_count; ?></div>
                    <p>Registered Users</p>
                    <a href="customers.php" class="btn btn-sm">View All</a>
                </div>

                <div class="stat-card">
                    <h3>Total Accounts</h3>
                    <div class="stat-value"><?php echo $total_accounts_count; ?></div>
                    <p>Active Accounts</p>
                    <a href="accounts.php" class="btn btn-sm">View All</a>
                </div>
            </section>

            <section class="dashboard-charts">
                <div class="chart-filters">
                    <h3>Transaction Analytics</h3>
                    <div class="filter-controls">
                        <div class="filter-group">
                            <label for="period-selector">Time Period:</label>
                            <select id="period-selector">
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly" selected>Monthly</option>
                                <option value="yearly">Yearly</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="start-date">From:</label>
                            <input type="date" id="start-date">
                        </div>
                        <div class="filter-group">
                            <label for="end-date">To:</label>
                            <input type="date" id="end-date">
                        </div>
                    </div>
                </div>

                <div class="charts-container">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h4>Transaction Trends Over Time</h4>
                            <button id="export-trends-btn" class="btn btn-sm export-btn">
                                <i class="fas fa-file-excel"></i> Export to Excel
                            </button>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="transaction-trends-chart"></canvas>
                        </div>
                    </div>

                    <div class="chart-row">
                        <div class="chart-card half-width">
                            <div class="chart-header">
                                <h4>Transaction Types Distribution</h4>
                                <button id="export-types-btn" class="btn btn-sm export-btn">
                                    <i class="fas fa-file-excel"></i> Export to Excel
                                </button>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="transaction-types-chart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card half-width">
                            <div class="chart-header">
                                <h4>Transaction Volume by Account Type</h4>
                                <button id="export-accounts-btn" class="btn btn-sm export-btn">
                                    <i class="fas fa-file-excel"></i> Export to Excel
                                </button>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="account-types-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="recent-activity">
                <h3>Recent Transactions</h3>

                <?php if ($recent_transactions_result->num_rows > 0): ?>
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Customer</th>
                                <th>Account</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($transaction = $recent_transactions_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $transaction['TransactionID']; ?></td>
                                    <td><?php echo htmlspecialchars($transaction['Name']); ?></td>
                                    <td><?php echo $transaction['AccountID']; ?></td>
                                    <td>
                                        <span class="<?php echo strtolower($transaction['Type']); ?>">
                                            <?php echo $transaction['Type']; ?>
                                        </span>
                                    </td>
                                    <td>$<?php echo number_format($transaction['Amount'], 2); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($transaction['Date'])); ?></td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                    <a href="transactions.php" class="view-all">View All Transactions</a>
                <?php else: ?>
                    <p>No recent transactions found.</p>
                <?php endif; ?>
            </section>

            <section class="quick-actions">
                <h3>Quick Actions</h3>
                <div class="action-buttons">
                    <a href="customers.php?action=add" class="btn">Add New Customer</a>
                    <a href="accounts.php?action=add" class="btn">Create New Account</a>
                    <a href="reports.php?type=daily" class="btn">Generate Daily Report</a>
                </div>
            </section>
        </main>

        <footer>
            <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
        </footer>
    </div>

    <script src="../js/admin.js"></script>
    <script src="js/dashboard-charts.js"></script>
</body>
</html>

<?php
$conn->close();
?>
