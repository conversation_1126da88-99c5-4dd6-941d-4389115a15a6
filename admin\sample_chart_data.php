<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

// Get request parameters
$chart_type = isset($_GET['chart']) ? $_GET['chart'] : '';
$time_period = isset($_GET['period']) ? $_GET['period'] : 'monthly';

// Sample data for testing
$response = [
    'success' => true,
    'data' => [],
    'labels' => [],
    'datasets' => []
];

switch ($chart_type) {
    case 'transaction_trends':
        // Sample transaction trends data
        if ($time_period == 'monthly') {
            $response['labels'] = ['Jan 2023', 'Feb 2023', 'Mar 2023', 'Apr 2023', 'May 2023', 'Jun 2023'];
            $response['datasets'] = [
                [
                    'label' => 'Credits',
                    'data' => [5000, 7500, 6200, 8100, 9500, 11000],
                    'backgroundColor' => 'rgba(76, 175, 80, 0.2)',
                    'borderColor' => 'rgba(76, 175, 80, 1)',
                    'borderWidth' => 1
                ],
                [
                    'label' => 'Debits',
                    'data' => [4200, 6300, 5100, 7200, 8300, 9100],
                    'backgroundColor' => 'rgba(244, 67, 54, 0.2)',
                    'borderColor' => 'rgba(244, 67, 54, 1)',
                    'borderWidth' => 1
                ],
                [
                    'label' => 'Transfers',
                    'data' => [3100, 4200, 3800, 5100, 6200, 7300],
                    'backgroundColor' => 'rgba(33, 150, 243, 0.2)',
                    'borderColor' => 'rgba(33, 150, 243, 1)',
                    'borderWidth' => 1
                ]
            ];
        } else if ($time_period == 'daily') {
            $response['labels'] = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
            $response['datasets'] = [
                [
                    'label' => 'Credits',
                    'data' => [1200, 1500, 1800, 1600, 2100, 1900, 1400],
                    'backgroundColor' => 'rgba(76, 175, 80, 0.2)',
                    'borderColor' => 'rgba(76, 175, 80, 1)',
                    'borderWidth' => 1
                ],
                [
                    'label' => 'Debits',
                    'data' => [1000, 1300, 1500, 1400, 1800, 1600, 1200],
                    'backgroundColor' => 'rgba(244, 67, 54, 0.2)',
                    'borderColor' => 'rgba(244, 67, 54, 1)',
                    'borderWidth' => 1
                ],
                [
                    'label' => 'Transfers',
                    'data' => [800, 1100, 1300, 1200, 1500, 1400, 1000],
                    'backgroundColor' => 'rgba(33, 150, 243, 0.2)',
                    'borderColor' => 'rgba(33, 150, 243, 1)',
                    'borderWidth' => 1
                ]
            ];
        } else {
            $response['labels'] = ['2021', '2022', '2023'];
            $response['datasets'] = [
                [
                    'label' => 'Credits',
                    'data' => [45000, 62000, 78000],
                    'backgroundColor' => 'rgba(76, 175, 80, 0.2)',
                    'borderColor' => 'rgba(76, 175, 80, 1)',
                    'borderWidth' => 1
                ],
                [
                    'label' => 'Debits',
                    'data' => [38000, 52000, 65000],
                    'backgroundColor' => 'rgba(244, 67, 54, 0.2)',
                    'borderColor' => 'rgba(244, 67, 54, 1)',
                    'borderWidth' => 1
                ],
                [
                    'label' => 'Transfers',
                    'data' => [25000, 35000, 48000],
                    'backgroundColor' => 'rgba(33, 150, 243, 0.2)',
                    'borderColor' => 'rgba(33, 150, 243, 1)',
                    'borderWidth' => 1
                ]
            ];
        }
        break;
        
    case 'transaction_types':
        // Sample transaction types data
        $response['labels'] = ['Credit', 'Debit', 'Transfer'];
        $response['datasets'] = [
            [
                'data' => [350, 280, 170],
                'backgroundColor' => [
                    'rgba(76, 175, 80, 0.7)',
                    'rgba(244, 67, 54, 0.7)',
                    'rgba(33, 150, 243, 0.7)'
                ],
                'borderWidth' => 1
            ]
        ];
        $response['amounts'] = [78000, 65000, 48000];
        break;
        
    case 'account_types':
        // Sample account types data
        $response['labels'] = ['Savings', 'Checking', 'Investment', 'Fixed Deposit'];
        $response['datasets'] = [
            [
                'label' => 'Transaction Count',
                'data' => [420, 380, 150, 50],
                'backgroundColor' => 'rgba(156, 39, 176, 0.7)',
                'borderColor' => 'rgba(156, 39, 176, 1)',
                'borderWidth' => 1
            ]
        ];
        $response['amounts'] = [95000, 85000, 65000, 25000];
        break;
        
    default:
        $response['success'] = false;
        $response['error'] = 'Invalid chart type';
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
?>
