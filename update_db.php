<?php
/**
 * Database Update Script
 * 
 * This script updates the database schema to support the email verification system.
 */

// Include database connection
require_once 'config/db_connect.php';

// Read SQL file
$sql_file = file_get_contents('sql/update_schema.sql');

// Split SQL file into individual queries
$queries = explode(';', $sql_file);

// Execute each query
$success = true;
$errors = [];

foreach ($queries as $query) {
    $query = trim($query);
    
    if (!empty($query)) {
        if (!$conn->query($query)) {
            $success = false;
            $errors[] = "Error executing query: " . $conn->error . " (Query: " . $query . ")";
        }
    }
}

// Create logs directory if it doesn't exist
$log_dir = __DIR__ . '/logs';
if (!file_exists($log_dir)) {
    mkdir($log_dir, 0755, true);
}

// Output results
if ($success) {
    echo "Database updated successfully!";
} else {
    echo "Error updating database:<br>";
    foreach ($errors as $error) {
        echo "- " . $error . "<br>";
    }
}

// Close connection
$conn->close();
?>
