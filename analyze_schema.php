<?php
/**
 * Database Schema Analysis for Admin Relationships
 */

require_once 'config/db_connect.php';

echo "<h1>Banking System Database Schema Analysis</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .issue { background-color: #ffe6e6; }
    .recommendation { background-color: #e6ffe6; }
</style>";

echo "<p class='info'>Analyzing database: banking_system_new</p>";

// Get all tables
$tables_result = $conn->query("SHOW TABLES");
$tables = [];
while ($row = $tables_result->fetch_row()) {
    $tables[] = $row[0];
}

echo "<h2>Current Tables</h2>";
echo "<ul>";
foreach ($tables as $table) {
    echo "<li>$table</li>";
}
echo "</ul>";

// Analyze each table structure
echo "<h2>Table Structures</h2>";

foreach ($tables as $table) {
    echo "<h3>Table: $table</h3>";
    
    // Get table structure
    $structure_result = $conn->query("DESCRIBE $table");
    echo "<table>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = $structure_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Check current foreign key relationships
echo "<h2>Current Foreign Key Relationships</h2>";
$fk_query = "
    SELECT 
        TABLE_NAME,
        COLUMN_NAME,
        CONSTRAINT_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
    FROM 
        INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
    WHERE 
        REFERENCED_TABLE_SCHEMA = 'banking_system_new'
        AND REFERENCED_TABLE_NAME IS NOT NULL
";

$fk_result = $conn->query($fk_query);
if ($fk_result->num_rows > 0) {
    echo "<table>";
    echo "<tr><th>Table</th><th>Column</th><th>References</th><th>Referenced Column</th></tr>";
    
    while ($row = $fk_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['TABLE_NAME'] . "</td>";
        echo "<td>" . $row['COLUMN_NAME'] . "</td>";
        echo "<td>" . $row['REFERENCED_TABLE_NAME'] . "</td>";
        echo "<td>" . $row['REFERENCED_COLUMN_NAME'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='warning'>⚠️ No foreign key relationships found!</p>";
}

// Identify missing admin relationships
echo "<h2>Schema Issues & Recommendations</h2>";

echo "<div class='issue'>";
echo "<h3>🚨 Issues Identified:</h3>";
echo "<ol>";
echo "<li><strong>Admin table is isolated</strong> - No foreign key relationships with other tables</li>";
echo "<li><strong>No audit trail</strong> - Cannot track which admin performed actions</li>";
echo "<li><strong>No approval workflow</strong> - No way to track admin approvals for accounts</li>";
echo "<li><strong>No transaction oversight</strong> - Cannot track admin involvement in transactions</li>";
echo "<li><strong>Missing admin roles</strong> - No role-based access control</li>";
echo "</ol>";
echo "</div>";

echo "<div class='recommendation'>";
echo "<h3>✅ Recommended Schema Improvements:</h3>";
echo "<ol>";
echo "<li><strong>Add admin approval tracking to pending_account table</strong></li>";
echo "<li><strong>Add admin roles and permissions system</strong></li>";
echo "<li><strong>Create admin_actions audit log table</strong></li>";
echo "<li><strong>Add admin oversight to high-value transactions</strong></li>";
echo "<li><strong>Track admin who created/modified accounts</strong></li>";
echo "<li><strong>Add admin session management</strong></li>";
echo "</ol>";
echo "</div>";

echo "<h2>Proposed New Schema Structure</h2>";
echo "<h3>1. Enhanced pending_account table</h3>";
echo "<pre>
ALTER TABLE pending_account 
ADD COLUMN ApprovedBy INT(11) NULL,
ADD COLUMN ApprovalDate DATETIME NULL,
ADD COLUMN ApprovalNotes TEXT NULL,
ADD FOREIGN KEY (ApprovedBy) REFERENCES admin(AdminID);
</pre>";

echo "<h3>2. Enhanced account table</h3>";
echo "<pre>
ALTER TABLE account 
ADD COLUMN CreatedBy INT(11) NULL,
ADD COLUMN CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN LastModifiedBy INT(11) NULL,
ADD COLUMN LastModifiedDate DATETIME NULL,
ADD FOREIGN KEY (CreatedBy) REFERENCES admin(AdminID),
ADD FOREIGN KEY (LastModifiedBy) REFERENCES admin(AdminID);
</pre>";

echo "<h3>3. New admin_roles table</h3>";
echo "<pre>
CREATE TABLE admin_roles (
    RoleID INT(11) AUTO_INCREMENT PRIMARY KEY,
    RoleName VARCHAR(50) NOT NULL,
    Description TEXT,
    Permissions JSON,
    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE admin 
ADD COLUMN RoleID INT(11) DEFAULT 1,
ADD FOREIGN KEY (RoleID) REFERENCES admin_roles(RoleID);
</pre>";

echo "<h3>4. New admin_actions audit table</h3>";
echo "<pre>
CREATE TABLE admin_actions (
    ActionID INT(11) AUTO_INCREMENT PRIMARY KEY,
    AdminID INT(11) NOT NULL,
    ActionType ENUM('CREATE', 'UPDATE', 'DELETE', 'APPROVE', 'REJECT', 'LOGIN', 'LOGOUT') NOT NULL,
    TableName VARCHAR(50),
    RecordID INT(11),
    OldValues JSON,
    NewValues JSON,
    ActionDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    IPAddress VARCHAR(45),
    UserAgent TEXT,
    FOREIGN KEY (AdminID) REFERENCES admin(AdminID)
);
</pre>";

echo "<h3>5. Enhanced transaction table</h3>";
echo "<pre>
ALTER TABLE transaction 
ADD COLUMN ProcessedBy INT(11) NULL,
ADD COLUMN RequiresApproval BOOLEAN DEFAULT FALSE,
ADD COLUMN ApprovedBy INT(11) NULL,
ADD COLUMN ApprovalDate DATETIME NULL,
ADD FOREIGN KEY (ProcessedBy) REFERENCES admin(AdminID),
ADD FOREIGN KEY (ApprovedBy) REFERENCES admin(AdminID);
</pre>";

echo "<p><a href='implement_schema_improvements.php' class='btn'>🚀 Implement These Improvements</a></p>";
echo "<p><a href='index.php'>← Back to Home</a></p>";

$conn->close();
?>
