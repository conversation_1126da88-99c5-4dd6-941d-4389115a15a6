<?php
// Output all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();

// Redirect if already logged in
if (isset($_SESSION['customer_id'])) {
    header("Location: dashboard.php");
    exit();
} elseif (isset($_SESSION['admin_id'])) {
    header("Location: admin/dashboard.php");
    exit();
}

echo "<h1>Signup Page - Debug Mode</h1>";

// Database connection
$host = "localhost";
$username = "root";
$password = "";
$database = "banking_system";

echo "<p>Connecting to database: $host, $username, $database</p>";

// Create connection
$conn = new mysqli($host, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    die("<p>Connection failed: " . $conn->connect_error . "</p>");
} else {
    echo "<p>Database connection successful.</p>";
}

// Check if branch table exists
$result = $conn->query("SHOW TABLES LIKE 'branch'");
if ($result->num_rows > 0) {
    echo "<p>Branch table exists.</p>";
} else {
    echo "<p>Branch table does not exist. Creating it now...</p>";
    
    // Create branch table
    $sql = "CREATE TABLE branch (
      BranchID int(11) NOT NULL AUTO_INCREMENT,
      BranchName varchar(100) NOT NULL DEFAULT 'Main Branch',
      Location varchar(255) NOT NULL DEFAULT 'Butuan',
      PRIMARY KEY (BranchID)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    if ($conn->query($sql)) {
        echo "<p>Branch table created successfully.</p>";
        
        // Insert branch data
        $sql = "INSERT INTO branch (BranchID, BranchName, Location) VALUES
        (1, 'Main Branch', 'Butuan'),
        (2, 'Downtown Branch', 'Butuan City Center'),
        (3, 'Sibagat Branch', 'Sibagat')";
        
        if ($conn->query($sql)) {
            echo "<p>Branch data inserted successfully.</p>";
        } else {
            echo "<p>Error inserting branch data: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Error creating branch table: " . $conn->error . "</p>";
    }
}

// Get branches for dropdown
echo "<p>Fetching branches from database...</p>";
$branches_sql = "SELECT BranchID, BranchName, Location FROM branch";
$branches_result = $conn->query($branches_sql);

if (!$branches_result) {
    echo "<p>Error fetching branches: " . $conn->error . "</p>";
} else {
    echo "<p>Branch query executed successfully.</p>";
    
    $branches = array();
    if ($branches_result->num_rows > 0) {
        echo "<p>Found " . $branches_result->num_rows . " branches:</p>";
        echo "<ul>";
        while($row = $branches_result->fetch_assoc()) {
            $branches[$row['BranchID']] = $row['BranchName'] . ' (' . $row['Location'] . ')';
            echo "<li>" . $row['BranchID'] . ": " . $row['BranchName'] . " (" . $row['Location'] . ")</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No branches found in the database.</p>";
    }
}

// Process signup form
$error = "";
$success = "";
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    echo "<p>Processing form submission...</p>";
    
    // Get form data
    $name = $conn->real_escape_string($_POST['name']);
    $email = $conn->real_escape_string($_POST['email']);
    $phone = $conn->real_escape_string($_POST['phone']);
    $address = $conn->real_escape_string($_POST['address']);
    $branch_id = intval($_POST['branch_id']);
    $account_type = $conn->real_escape_string($_POST['account_type']);
    $initial_deposit = floatval($_POST['initial_deposit']);
    
    echo "<p>Form data received: Name=$name, Email=$email, Phone=$phone, Branch=$branch_id, Account Type=$account_type, Initial Deposit=$initial_deposit</p>";
    
    // Validate form data
    if (empty($name) || empty($email) || empty($phone) || empty($address)) {
        $error = "All fields are required";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } elseif ($initial_deposit < 500) {
        $error = "Initial deposit must be at least $500";
    } else {
        // Check if email already exists
        $check_email = "SELECT CustomerID FROM customer WHERE Email = '$email'";
        $email_result = $conn->query($check_email);
        
        if (!$email_result) {
            $error = "Error checking email: " . $conn->error;
        } elseif ($email_result->num_rows > 0) {
            $error = "Email already registered. Please use a different email.";
        } else {
            // Generate verification code
            $verification_code = md5(uniqid(rand(), true));
            
            echo "<p>Starting transaction...</p>";
            
            // Start transaction
            $conn->begin_transaction();
            
            try {
                // Insert customer
                $insert_customer = "INSERT INTO customer (Name, Phone, Email, Address, Status, VerificationCode) 
                                   VALUES ('$name', '$phone', '$email', '$address', 'pending', '$verification_code')";
                
                echo "<p>Executing query: $insert_customer</p>";
                
                if (!$conn->query($insert_customer)) {
                    throw new Exception("Error inserting customer: " . $conn->error);
                }
                
                $customer_id = $conn->insert_id;
                echo "<p>Customer inserted with ID: $customer_id</p>";
                
                // Insert pending account request
                $insert_request = "INSERT INTO pending_account (CustomerID, AccountType, BranchID, InitialDeposit) 
                                  VALUES ($customer_id, '$account_type', $branch_id, $initial_deposit)";
                
                echo "<p>Executing query: $insert_request</p>";
                
                if (!$conn->query($insert_request)) {
                    throw new Exception("Error inserting account request: " . $conn->error);
                }
                
                echo "<p>Account request inserted successfully.</p>";
                
                // Commit transaction
                $conn->commit();
                echo "<p>Transaction committed.</p>";
                
                // Send verification email (in a real application)
                // mail($email, "Verify Your Email", "Click the link to verify: http://yourdomain.com/verify.php?code=$verification_code");
                
                $success = "Your account request has been submitted successfully! Please check your email to verify your account.";
                
                // Clear form data
                $name = $email = $phone = $address = "";
                $branch_id = $account_type = "";
                $initial_deposit = 500;
                
            } catch (Exception $e) {
                // Rollback transaction on error
                $conn->rollback();
                echo "<p>Transaction rolled back due to error.</p>";
                $error = "Registration failed: " . $e->getMessage();
            }
        }
    }
}

$conn->close();
echo "<p>Database connection closed.</p>";

echo "<p><a href='signup.php'>Go to Regular Signup Page</a></p>";
echo "<p><a href='index.php'>Go to Homepage</a></p>";
?>
