<?php
// Output all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Creating Remaining Tables in banking_system_new</h1>";

// Database connection parameters
$host = "localhost";
$username = "root";
$password = "";
$database = "banking_system_new";

// Create connection
$conn = new mysqli($host, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    die("<p>Connection failed: " . $conn->connect_error . "</p>");
} else {
    echo "<p>Successfully connected to $database database.</p>";
}

// Create customer table
echo "<h2>Creating Customer Table</h2>";
$sql = "CREATE TABLE IF NOT EXISTS customer (
  CustomerID int(11) NOT NULL AUTO_INCREMENT,
  Name varchar(100) NOT NULL DEFAULT 'Wendelyn Ferrer',
  Phone varchar(15) NOT NULL DEFAULT '**************',
  Email varchar(100) NOT NULL DEFAULT '<EMAIL>',
  Address varchar(255) NOT NULL DEFAULT 'Sibagat',
  Status enum('pending', 'active', 'suspended') NOT NULL DEFAULT 'pending',
  VerificationCode varchar(32) DEFAULT NULL,
  IsVerified tinyint(1) NOT NULL DEFAULT 0,
  RegistrationDate datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (CustomerID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql)) {
    echo "<p>Customer table created successfully.</p>";
    
    // Insert sample customer data
    $sql = "INSERT INTO customer (CustomerID, Name, Phone, Email, Address, Status, IsVerified) VALUES
    (1, 'Wendelyn Ferrer', '**************', '<EMAIL>', 'Sibagat', 'active', 1),
    (2, 'John Smith', '***********', '<EMAIL>', 'Butuan City', 'active', 1),
    (3, 'Maria Garcia', '***********', '<EMAIL>', 'Ampayon', 'active', 1)";
    
    if ($conn->query($sql)) {
        echo "<p>Sample customer data inserted successfully.</p>";
    } else {
        echo "<p>Error inserting customer data: " . $conn->error . "</p>";
    }
} else {
    echo "<p>Error creating customer table: " . $conn->error . "</p>";
}

// Create account table
echo "<h2>Creating Account Table</h2>";
$sql = "CREATE TABLE IF NOT EXISTS account (
  AccountID int(11) NOT NULL AUTO_INCREMENT,
  CustomerID int(11) NOT NULL,
  BranchID int(11) NOT NULL,
  AccountType varchar(50) NOT NULL DEFAULT 'Savings',
  Balance decimal(15,2) NOT NULL DEFAULT 0.00,
  PRIMARY KEY (AccountID),
  KEY CustomerID (CustomerID),
  KEY BranchID (BranchID),
  CONSTRAINT account_ibfk_1 FOREIGN KEY (CustomerID) REFERENCES customer (CustomerID),
  CONSTRAINT account_ibfk_2 FOREIGN KEY (BranchID) REFERENCES branch (BranchID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql)) {
    echo "<p>Account table created successfully.</p>";
    
    // Insert sample account data
    $sql = "INSERT INTO account (AccountID, CustomerID, BranchID, AccountType, Balance) VALUES
    (101, 1, 1, 'Savings', 5000.00),
    (102, 1, 1, 'Checking', 3500.00),
    (103, 2, 2, 'Savings', 7500.00),
    (104, 2, 2, 'Investment', 15000.00),
    (105, 3, 3, 'Savings', 4200.00)";
    
    if ($conn->query($sql)) {
        echo "<p>Sample account data inserted successfully.</p>";
    } else {
        echo "<p>Error inserting account data: " . $conn->error . "</p>";
    }
} else {
    echo "<p>Error creating account table: " . $conn->error . "</p>";
}

// Create transaction table
echo "<h2>Creating Transaction Table</h2>";
$sql = "CREATE TABLE IF NOT EXISTS transaction (
  TransactionID int(11) NOT NULL AUTO_INCREMENT,
  AccountID int(11) NOT NULL,
  Amount decimal(15,2) NOT NULL DEFAULT 5.01,
  Date date DEFAULT NULL,
  Type varchar(50) NOT NULL DEFAULT 'Credit',
  PRIMARY KEY (TransactionID),
  KEY AccountID (AccountID),
  CONSTRAINT transaction_ibfk_1 FOREIGN KEY (AccountID) REFERENCES account (AccountID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql)) {
    echo "<p>Transaction table created successfully.</p>";
    
    // Insert sample transaction data
    $sql = "INSERT INTO transaction (TransactionID, AccountID, Amount, Date, Type) VALUES
    (1, 101, 1000.00, '2025-01-15', 'Credit'),
    (2, 101, 500.00, '2025-01-20', 'Debit'),
    (3, 102, 1500.00, '2025-01-22', 'Credit'),
    (4, 101, 2000.00, '2025-01-25', 'Credit'),
    (5, 103, 1000.00, '2025-01-26', 'Credit'),
    (6, 103, 300.00, '2025-01-28', 'Debit'),
    (7, 104, 5000.00, '2025-01-30', 'Credit'),
    (8, 105, 1200.00, '2025-02-01', 'Credit'),
    (9, 102, 800.00, '2025-02-03', 'Debit'),
    (10, 101, 1500.00, '2025-02-05', 'Transfer')";
    
    if ($conn->query($sql)) {
        echo "<p>Sample transaction data inserted successfully.</p>";
    } else {
        echo "<p>Error inserting transaction data: " . $conn->error . "</p>";
    }
} else {
    echo "<p>Error creating transaction table: " . $conn->error . "</p>";
}

// Create admin table
echo "<h2>Creating Admin Table</h2>";
$sql = "CREATE TABLE IF NOT EXISTS admin (
  AdminID int(11) NOT NULL AUTO_INCREMENT,
  Username varchar(50) NOT NULL,
  Password varchar(255) NOT NULL,
  Name varchar(100) NOT NULL,
  Email varchar(100) NOT NULL,
  LastLogin datetime DEFAULT NULL,
  PRIMARY KEY (AdminID),
  UNIQUE KEY Username (Username)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql)) {
    echo "<p>Admin table created successfully.</p>";
    
    // Insert default admin user (password: admin123)
    $sql = "INSERT INTO admin (Username, Password, Name, Email) VALUES
    ('admin', '$2y$10$qeS0HEh7urRAiMHxgVjkNu.lQR6zRsmRpDiZrPjVcNlOYHkLgPkry', 'System Administrator', '<EMAIL>')";
    
    if ($conn->query($sql)) {
        echo "<p>Default admin user created successfully.</p>";
    } else {
        echo "<p>Error creating admin user: " . $conn->error . "</p>";
    }
} else {
    echo "<p>Error creating admin table: " . $conn->error . "</p>";
}

// Create pending_account table
echo "<h2>Creating Pending Account Table</h2>";
$sql = "CREATE TABLE IF NOT EXISTS pending_account (
  RequestID int(11) NOT NULL AUTO_INCREMENT,
  CustomerID int(11) NOT NULL,
  AccountType varchar(50) NOT NULL DEFAULT 'Savings',
  BranchID int(11) NOT NULL,
  InitialDeposit decimal(15,2) NOT NULL DEFAULT 0.00,
  RequestDate datetime DEFAULT CURRENT_TIMESTAMP,
  Status enum('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
  PRIMARY KEY (RequestID),
  KEY CustomerID (CustomerID),
  KEY BranchID (BranchID),
  CONSTRAINT pending_account_ibfk_1 FOREIGN KEY (CustomerID) REFERENCES customer (CustomerID),
  CONSTRAINT pending_account_ibfk_2 FOREIGN KEY (BranchID) REFERENCES branch (BranchID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql)) {
    echo "<p>Pending Account table created successfully.</p>";
} else {
    echo "<p>Error creating pending account table: " . $conn->error . "</p>";
}

// Verify all tables
echo "<h2>Verifying All Tables</h2>";
$result = $conn->query("SHOW TABLES");
if ($result->num_rows > 0) {
    echo "<p>Tables in the database:</p>";
    echo "<ul>";
    while($row = $result->fetch_row()) {
        echo "<li>" . $row[0] . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No tables found in the database.</p>";
}

// Close connection
$conn->close();

echo "<p><a href='signup.php'>Try Signup Page Again</a></p>";
echo "<p><a href='index.php'>Go to Homepage</a></p>";
?>
