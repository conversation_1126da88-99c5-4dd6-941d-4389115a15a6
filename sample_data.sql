-- Sample data for banking_system database

-- Insert branch data
INSERT INTO `branch` (`BranchID`, `BranchName`, `Location`) VALUES
(1, 'Main Branch', 'Butuan'),
(2, 'Downtown Branch', 'Butuan City Center'),
(3, 'Sibagat Branch', 'Sibagat');

-- Insert customer data
INSERT INTO `customer` (`CustomerID`, `Name`, `Phone`, `Email`, `Address`) VALUES
(1, '<PERSON><PERSON><PERSON> Ferrer', '**************', '<EMAIL>', 'Sibagat'),
(2, '<PERSON>', '***********', '<EMAIL>', 'Butuan City'),
(3, '<PERSON>', '***********', '<EMAIL>', 'Ampayon');

-- Insert account data
INSERT INTO `account` (`AccountID`, `CustomerID`, `BranchID`, `AccountType`, `Balance`) VALUES
(101, 1, 1, 'Savings', 5000.00),
(102, 1, 1, 'Checking', 3500.00),
(103, 2, 2, 'Savings', 7500.00),
(104, 2, 2, 'Investment', 15000.00),
(105, 3, 3, 'Savings', 4200.00);

-- Insert transaction data
INSERT INTO `transaction` (`TransactionID`, `AccountID`, `Amount`, `Date`, `Type`) VALUES
(1, 101, 1000.00, '2025-01-15', 'Credit'),
(2, 101, 500.00, '2025-01-20', 'Debit'),
(3, 102, 1500.00, '2025-01-22', 'Credit'),
(4, 101, 2000.00, '2025-01-25', 'Credit'),
(5, 103, 1000.00, '2025-01-26', 'Credit'),
(6, 103, 300.00, '2025-01-28', 'Debit'),
(7, 104, 5000.00, '2025-01-30', 'Credit'),
(8, 105, 1200.00, '2025-02-01', 'Credit'),
(9, 102, 800.00, '2025-02-03', 'Debit'),
(10, 101, 1500.00, '2025-02-05', 'Transfer');
