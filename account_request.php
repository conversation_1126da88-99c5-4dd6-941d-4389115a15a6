<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['customer_id'])) {
    header("Location: index.php");
    exit();
}

require_once 'config/db_connect.php';

// Get customer information
$customer_id = $_SESSION['customer_id'];
$customer_name = $_SESSION['customer_name'];

// Get branches for dropdown
$branches_sql = "SELECT BranchID, BranchName, Location FROM branch";
$branches_result = $conn->query($branches_sql);

$branches = array();
if ($branches_result->num_rows > 0) {
    while($row = $branches_result->fetch_assoc()) {
        $branches[$row['BranchID']] = $row['BranchName'] . ' (' . $row['Location'] . ')';
    }
}

// Process account request form
$error = "";
$success = "";
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $branch_id = intval($_POST['branch_id']);
    $account_type = $conn->real_escape_string($_POST['account_type']);
    $initial_deposit = floatval($_POST['initial_deposit']);
    
    // Validate form data
    if (empty($branch_id) || empty($account_type)) {
        $error = "All fields are required";
    } elseif ($initial_deposit < 500) {
        $error = "Initial deposit must be at least $500";
    } else {
        // Insert pending account request
        $insert_sql = "INSERT INTO pending_account (CustomerID, AccountType, BranchID, InitialDeposit) 
                      VALUES ($customer_id, '$account_type', $branch_id, $initial_deposit)";
        
        if ($conn->query($insert_sql)) {
            $success = "Your account request has been submitted successfully! It will be reviewed by an administrator.";
            
            // Clear form data
            $branch_id = $account_type = "";
            $initial_deposit = 500;
        } else {
            $error = "Error submitting request: " . $conn->error;
        }
    }
}

// Get existing pending requests
$pending_sql = "SELECT pa.RequestID, pa.AccountType, pa.InitialDeposit, pa.RequestDate, pa.Status, b.BranchName 
               FROM pending_account pa 
               JOIN branch b ON pa.BranchID = b.BranchID 
               WHERE pa.CustomerID = $customer_id 
               ORDER BY pa.RequestDate DESC";
$pending_result = $conn->query($pending_sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Request New Account</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/landing.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Banking System</h1>
            <div class="user-info">
                <p>Welcome, <?php echo htmlspecialchars($customer_name); ?></p>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </header>
        
        <nav>
            <ul>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="transactions.php">Transactions</a></li>
                <li><a href="transfer.php">Transfer Funds</a></li>
                <li><a href="account_request.php" class="active">Request New Account</a></li>
                <li><a href="profile.php">My Profile</a></li>
            </ul>
        </nav>
        
        <main>
            <section class="request-section">
                <h2>Request a New Account</h2>
                
                <?php if (!empty($error)): ?>
                    <div class="error-message"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if (!empty($success)): ?>
                    <div class="success-message"><?php echo $success; ?></div>
                <?php endif; ?>
                
                <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" id="account-request-form">
                    <div class="form-group">
                        <label for="account_type">Account Type:</label>
                        <select id="account_type" name="account_type" required>
                            <option value="">Select Account Type</option>
                            <option value="Savings" <?php echo (isset($account_type) && $account_type == 'Savings') ? 'selected' : ''; ?>>Savings Account</option>
                            <option value="Checking" <?php echo (isset($account_type) && $account_type == 'Checking') ? 'selected' : ''; ?>>Checking Account</option>
                            <option value="Investment" <?php echo (isset($account_type) && $account_type == 'Investment') ? 'selected' : ''; ?>>Investment Account</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="branch_id">Preferred Branch:</label>
                        <select id="branch_id" name="branch_id" required>
                            <option value="">Select Branch</option>
                            <?php foreach ($branches as $id => $name): ?>
                                <option value="<?php echo $id; ?>" <?php echo (isset($branch_id) && $branch_id == $id) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="initial_deposit">Initial Deposit Amount ($):</label>
                        <input type="number" id="initial_deposit" name="initial_deposit" min="500" step="0.01" value="<?php echo isset($initial_deposit) ? htmlspecialchars($initial_deposit) : '500'; ?>" required>
                        <small>Minimum deposit: $500</small>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn">Submit Request</button>
                    </div>
                </form>
            </section>
            
            <section class="pending-requests">
                <h2>Your Pending Account Requests</h2>
                
                <?php if ($pending_result->num_rows > 0): ?>
                    <table class="transactions-table">
                        <thead>
                            <tr>
                                <th>Request ID</th>
                                <th>Account Type</th>
                                <th>Branch</th>
                                <th>Initial Deposit</th>
                                <th>Request Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($request = $pending_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $request['RequestID']; ?></td>
                                    <td><?php echo htmlspecialchars($request['AccountType']); ?></td>
                                    <td><?php echo htmlspecialchars($request['BranchName']); ?></td>
                                    <td>$<?php echo number_format($request['InitialDeposit'], 2); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($request['RequestDate'])); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo strtolower($request['Status']); ?>">
                                            <?php echo ucfirst($request['Status']); ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>You have no pending account requests.</p>
                <?php endif; ?>
            </section>
        </main>
        
        <footer>
            <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
        </footer>
    </div>
    
    <script src="js/script.js"></script>
</body>
</html>

<?php
$conn->close();
?>
