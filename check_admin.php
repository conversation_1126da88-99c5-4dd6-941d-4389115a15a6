<?php
/**
 * Check Admin User Setup
 */

require_once 'config/db_connect.php';

echo "<p class='info'>Using database: banking_system_new</p>";

echo "<h1>Admin User Check</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

echo "<h2>Database Connection Test</h2>";
if ($conn->connect_error) {
    echo "<p class='error'>❌ Database connection failed: " . $conn->connect_error . "</p>";
    exit();
} else {
    echo "<p class='success'>✅ Database connection successful</p>";
}

echo "<h2>Checking Tables</h2>";

// Check if admin table exists
$result = $conn->query("SHOW TABLES LIKE 'admin'");
if ($result->num_rows > 0) {
    echo "<p class='success'>✅ Admin table exists</p>";

    // Check admin users
    $result = $conn->query("SELECT * FROM admin");
    if ($result->num_rows > 0) {
        echo "<p class='success'>✅ Admin users found: " . $result->num_rows . "</p>";

        echo "<h3>Current Admin Users:</h3>";
        echo "<table>";
        echo "<tr><th>AdminID</th><th>Username</th><th>Name</th><th>Email</th><th>Password Hash</th></tr>";

        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['AdminID'] . "</td>";
            echo "<td>" . $row['Username'] . "</td>";
            echo "<td>" . $row['Name'] . "</td>";
            echo "<td>" . $row['Email'] . "</td>";
            echo "<td>" . substr($row['Password'], 0, 20) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";

    } else {
        echo "<p class='warning'>⚠️ Admin table exists but no admin users found</p>";
        echo "<p class='info'>Creating default admin user...</p>";

        // Create default admin user
        $username = 'admin';
        $password = 'admin123';
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        $name = 'System Administrator';
        $email = '<EMAIL>';

        $sql = "INSERT INTO admin (Username, Password, Name, Email) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssss", $username, $hashed_password, $name, $email);

        if ($stmt->execute()) {
            echo "<p class='success'>✅ Default admin user created successfully!</p>";
            echo "<p class='info'>Username: admin</p>";
            echo "<p class='info'>Password: admin123</p>";
        } else {
            echo "<p class='error'>❌ Error creating admin user: " . $conn->error . "</p>";
        }
    }

} else {
    echo "<p class='error'>❌ Admin table does not exist</p>";
    echo "<p class='info'>Creating admin table...</p>";

    // Create admin table
    $sql = "CREATE TABLE admin (
        AdminID int(11) NOT NULL AUTO_INCREMENT,
        Username varchar(50) NOT NULL,
        Password varchar(255) NOT NULL,
        Name varchar(100) NOT NULL,
        Email varchar(100) NOT NULL,
        LastLogin datetime DEFAULT NULL,
        PRIMARY KEY (AdminID),
        UNIQUE KEY Username (Username)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    if ($conn->query($sql)) {
        echo "<p class='success'>✅ Admin table created successfully</p>";

        // Create default admin user
        $username = 'admin';
        $password = 'admin123';
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        $name = 'System Administrator';
        $email = '<EMAIL>';

        $sql = "INSERT INTO admin (Username, Password, Name, Email) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssss", $username, $hashed_password, $name, $email);

        if ($stmt->execute()) {
            echo "<p class='success'>✅ Default admin user created successfully!</p>";
            echo "<p class='info'>Username: admin</p>";
            echo "<p class='info'>Password: admin123</p>";
        } else {
            echo "<p class='error'>❌ Error creating admin user: " . $conn->error . "</p>";
        }

    } else {
        echo "<p class='error'>❌ Error creating admin table: " . $conn->error . "</p>";
    }
}

echo "<h2>All Tables in Database</h2>";
$result = $conn->query("SHOW TABLES");
if ($result->num_rows > 0) {
    echo "<ul>";
    while ($row = $result->fetch_row()) {
        echo "<li>" . $row[0] . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p class='warning'>⚠️ No tables found in database</p>";
    echo "<p class='info'>You may need to run the database setup script</p>";
}

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>Try logging in as admin with username: <strong>admin</strong> and password: <strong>admin123</strong></li>";
echo "<li>If login still fails, check the admin login form for any issues</li>";
echo "<li>Access admin login: <a href='admin/login.php'>admin/login.php</a></li>";
echo "<li>Access main site: <a href='index.php'>index.php</a></li>";
echo "</ol>";

$conn->close();
?>
