<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config/db_connect.php';

// Get admin information
$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];

// Handle account request approvals
$message = "";
$error = "";

if (isset($_GET['action']) && $_GET['action'] == 'approve' && isset($_GET['id'])) {
    $request_id = intval($_GET['id']);
    
    // Get request details
    $request_sql = "SELECT * FROM pending_account WHERE RequestID = $request_id AND Status = 'pending'";
    $request_result = $conn->query($request_sql);
    
    if ($request_result->num_rows == 1) {
        $request = $request_result->fetch_assoc();
        $customer_id = $request['CustomerID'];
        $account_type = $request['AccountType'];
        $branch_id = $request['BranchID'];
        $initial_deposit = $request['InitialDeposit'];
        
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Create new account
            $create_account = "INSERT INTO account (CustomerID, BranchID, AccountType, Balance) 
                              VALUES ($customer_id, $branch_id, '$account_type', $initial_deposit)";
            $conn->query($create_account);
            $account_id = $conn->insert_id;
            
            // Record initial deposit transaction
            $current_date = date('Y-m-d');
            $insert_transaction = "INSERT INTO transaction (AccountID, Amount, Date, Type) 
                                  VALUES ($account_id, $initial_deposit, '$current_date', 'Credit')";
            $conn->query($insert_transaction);
            
            // Update request status
            $update_request = "UPDATE pending_account SET Status = 'approved' WHERE RequestID = $request_id";
            $conn->query($update_request);
            
            // Update customer status if needed
            $update_customer = "UPDATE customer SET Status = 'active' WHERE CustomerID = $customer_id AND Status = 'pending'";
            $conn->query($update_customer);
            
            // Commit transaction
            $conn->commit();
            
            $message = "Account request approved successfully!";
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $error = "Error approving account request: " . $e->getMessage();
        }
    } else {
        $error = "Invalid account request or already processed.";
    }
}

if (isset($_GET['action']) && $_GET['action'] == 'reject' && isset($_GET['id'])) {
    $request_id = intval($_GET['id']);
    
    // Update request status
    $update_sql = "UPDATE pending_account SET Status = 'rejected' WHERE RequestID = $request_id AND Status = 'pending'";
    
    if ($conn->query($update_sql)) {
        $message = "Account request rejected successfully!";
    } else {
        $error = "Error rejecting account request: " . $conn->error;
    }
}

// Get filter parameters
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
$customer_id = isset($_GET['customer_id']) ? intval($_GET['customer_id']) : 0;

// Determine which view to show
$view = isset($_GET['view']) ? $_GET['view'] : 'accounts';

if ($view == 'pending') {
    // Show pending account requests
    $requests_sql = "SELECT pa.RequestID, pa.CustomerID, pa.AccountType, pa.BranchID, pa.InitialDeposit, 
                    pa.RequestDate, pa.Status, c.Name as CustomerName, b.BranchName 
                    FROM pending_account pa 
                    JOIN customer c ON pa.CustomerID = c.CustomerID 
                    JOIN branch b ON pa.BranchID = b.BranchID 
                    WHERE pa.Status = 'pending'";
    
    if ($customer_id > 0) {
        $requests_sql .= " AND pa.CustomerID = $customer_id";
    }
    
    $requests_sql .= " ORDER BY pa.RequestDate DESC";
    $requests_result = $conn->query($requests_sql);
} else {
    // Show existing accounts
    $accounts_sql = "SELECT a.AccountID, a.CustomerID, a.AccountType, a.Balance, a.BranchID, 
                    c.Name as CustomerName, b.BranchName 
                    FROM account a 
                    JOIN customer c ON a.CustomerID = c.CustomerID 
                    JOIN branch b ON a.BranchID = b.BranchID";
    
    $where_clauses = array();
    
    if ($customer_id > 0) {
        $where_clauses[] = "a.CustomerID = $customer_id";
    }
    
    if (!empty($where_clauses)) {
        $accounts_sql .= " WHERE " . implode(" AND ", $where_clauses);
    }
    
    $accounts_sql .= " ORDER BY a.AccountID DESC";
    $accounts_result = $conn->query($accounts_sql);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Manage Accounts</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body>
    <div class="container">
        <header class="admin-header">
            <h1>Banking System Admin</h1>
            <div class="user-info">
                <p>Welcome, <?php echo htmlspecialchars($admin_name); ?></p>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </header>
        
        <nav class="admin-nav">
            <ul>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="customers.php">Manage Customers</a></li>
                <li><a href="accounts.php" class="active">Manage Accounts</a></li>
                <li><a href="transactions.php">Transactions</a></li>
                <li><a href="reports.php">Reports</a></li>
            </ul>
        </nav>
        
        <main class="admin-main">
            <h2>Manage Accounts</h2>
            
            <?php if (!empty($message)): ?>
                <div class="success-message"><?php echo $message; ?></div>
            <?php endif; ?>
            
            <?php if (!empty($error)): ?>
                <div class="error-message"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <div class="view-tabs">
                <a href="accounts.php?view=accounts" class="tab <?php echo ($view != 'pending') ? 'active' : ''; ?>">Active Accounts</a>
                <a href="accounts.php?view=pending" class="tab <?php echo ($view == 'pending') ? 'active' : ''; ?>">Pending Requests</a>
            </div>
            
            <?php if ($view == 'pending'): ?>
                <!-- Pending Account Requests View -->
                <section class="account-requests">
                    <?php if ($requests_result->num_rows > 0): ?>
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>Request ID</th>
                                    <th>Customer</th>
                                    <th>Account Type</th>
                                    <th>Branch</th>
                                    <th>Initial Deposit</th>
                                    <th>Request Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while($request = $requests_result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo $request['RequestID']; ?></td>
                                        <td><?php echo htmlspecialchars($request['CustomerName']); ?></td>
                                        <td><?php echo htmlspecialchars($request['AccountType']); ?></td>
                                        <td><?php echo htmlspecialchars($request['BranchName']); ?></td>
                                        <td>$<?php echo number_format($request['InitialDeposit'], 2); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($request['RequestDate'])); ?></td>
                                        <td class="actions">
                                            <a href="accounts.php?action=approve&id=<?php echo $request['RequestID']; ?>&view=pending" class="btn btn-sm btn-success" onclick="return confirm('Are you sure you want to approve this account request?')">Approve</a>
                                            <a href="accounts.php?action=reject&id=<?php echo $request['RequestID']; ?>&view=pending" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to reject this account request?')">Reject</a>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <p>No pending account requests found.</p>
                    <?php endif; ?>
                </section>
            <?php else: ?>
                <!-- Active Accounts View -->
                <section class="accounts-list">
                    <?php if ($accounts_result->num_rows > 0): ?>
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>Account ID</th>
                                    <th>Customer</th>
                                    <th>Account Type</th>
                                    <th>Branch</th>
                                    <th>Balance</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while($account = $accounts_result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo $account['AccountID']; ?></td>
                                        <td><?php echo htmlspecialchars($account['CustomerName']); ?></td>
                                        <td><?php echo htmlspecialchars($account['AccountType']); ?></td>
                                        <td><?php echo htmlspecialchars($account['BranchName']); ?></td>
                                        <td>$<?php echo number_format($account['Balance'], 2); ?></td>
                                        <td class="actions">
                                            <a href="account_details.php?id=<?php echo $account['AccountID']; ?>" class="btn btn-sm">View Details</a>
                                            <a href="transactions.php?account_id=<?php echo $account['AccountID']; ?>" class="btn btn-sm">Transactions</a>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <p>No accounts found.</p>
                    <?php endif; ?>
                </section>
            <?php endif; ?>
        </main>
        
        <footer>
            <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
        </footer>
    </div>
    
    <script src="../js/admin.js"></script>
</body>
</html>

<?php
$conn->close();
?>
