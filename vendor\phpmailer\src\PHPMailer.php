<?php
namespace PHPMailer\PHPMailer;

class PHPMailer {
    public $ErrorInfo = "";
    public $SMTPDebug = 0;

    // Constants for encryption type
    const ENCRYPTION_STARTTLS = "tls";
    const ENCRYPTION_SMTPS = "ssl";

    // Constants for character sets
    const CHARSET_ISO88591 = "iso-8859-1";
    const CHARSET_UTF8 = "utf-8";

    // Constants for message priorities
    const PRIORITY_HIGH = 1;
    const PRIORITY_NORMAL = 3;
    const PRIORITY_LOW = 5;

    public function isSMTP() {
        return $this;
    }

    public function setFrom($email, $name = "") {
        return $this;
    }

    public function addAddress($email, $name = "") {
        return $this;
    }

    public function addReplyTo($email, $name = "") {
        return $this;
    }

    public function isHTML($isHtml = true) {
        return $this;
    }

    public function addAttachment($path, $name = "") {
        return $this;
    }

    public function send() {
        // In development mode, always return true
        return true;
    }
}