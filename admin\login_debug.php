<?php
// Output all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();

echo "<h1>Admin Login Debug</h1>";

// Database connection
require_once '../config/db_connect.php';

// Check admin table
$result = $conn->query("SELECT * FROM admin");
if ($result) {
    echo "<p>Admin table exists and is accessible.</p>";
    
    if ($result->num_rows > 0) {
        echo "<p>Found " . $result->num_rows . " admin users:</p>";
        echo "<ul>";
        while($row = $result->fetch_assoc()) {
            echo "<li>ID: " . $row['AdminID'] . ", Username: " . $row['Username'] . ", Name: " . $row['Name'] . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No admin users found in the database.</p>";
    }
} else {
    echo "<p>Error accessing admin table: " . $conn->error . "</p>";
}

// Process login form
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    echo "<h2>Processing Login Form</h2>";
    
    $username = $conn->real_escape_string($_POST['username']);
    $password = $_POST['password'];
    
    echo "<p>Submitted credentials: Username = $username, Password = [hidden]</p>";
    
    // Validate login credentials
    $sql = "SELECT AdminID, Username, Password, Name FROM admin WHERE Username = '$username'";
    echo "<p>Executing query: $sql</p>";
    
    $result = $conn->query($sql);
    
    if (!$result) {
        echo "<p>Query error: " . $conn->error . "</p>";
    } elseif ($result->num_rows == 1) {
        echo "<p>User found in database.</p>";
        
        $row = $result->fetch_assoc();
        echo "<p>Stored password hash: " . $row['Password'] . "</p>";
        
        // Verify password
        if (password_verify($password, $row['Password'])) {
            echo "<p>Password verification successful!</p>";
            
            $_SESSION['admin_id'] = $row['AdminID'];
            $_SESSION['admin_username'] = $row['Username'];
            $_SESSION['admin_name'] = $row['Name'];
            
            echo "<p>Session variables set:</p>";
            echo "<ul>";
            echo "<li>admin_id: " . $_SESSION['admin_id'] . "</li>";
            echo "<li>admin_username: " . $_SESSION['admin_username'] . "</li>";
            echo "<li>admin_name: " . $_SESSION['admin_name'] . "</li>";
            echo "</ul>";
            
            echo "<p>Login successful! <a href='dashboard.php'>Go to Dashboard</a></p>";
        } else {
            echo "<p>Password verification failed.</p>";
            
            // Try with different hash algorithms
            echo "<p>Trying different hash algorithms:</p>";
            echo "<ul>";
            echo "<li>MD5: " . (md5($password) == $row['Password'] ? "Match" : "No match") . "</li>";
            echo "<li>SHA1: " . (sha1($password) == $row['Password'] ? "Match" : "No match") . "</li>";
            echo "<li>Plain text: " . ($password == $row['Password'] ? "Match" : "No match") . "</li>";
            echo "</ul>";
        }
    } else {
        echo "<p>User not found in database.</p>";
    }
}

// Close connection
$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login Debug</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; }
        h1, h2 { color: #333; }
        form { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        label { display: block; margin-bottom: 5px; }
        input { width: 100%; padding: 8px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 3px; }
        button { background: #4CAF50; color: white; padding: 10px 15px; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #45a049; }
    </style>
</head>
<body>
    <h2>Test Admin Login</h2>
    
    <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
        <div>
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" value="admin" required>
        </div>
        
        <div>
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" value="admin123" required>
        </div>
        
        <button type="submit">Test Login</button>
    </form>
    
    <p><a href="login.php">Go to Regular Login Page</a></p>
</body>
</html>
