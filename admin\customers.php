<?php
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config/db_connect.php';

// Get admin information
$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];

// Handle customer status updates
$message = "";
$error = "";

if (isset($_GET['action']) && $_GET['action'] == 'approve' && isset($_GET['id'])) {
    $customer_id = intval($_GET['id']);
    
    // Update customer status
    $update_sql = "UPDATE customer SET Status = 'active' WHERE CustomerID = $customer_id AND Status = 'pending'";
    
    if ($conn->query($update_sql)) {
        $message = "Customer approved successfully!";
    } else {
        $error = "Error approving customer: " . $conn->error;
    }
}

if (isset($_GET['action']) && $_GET['action'] == 'suspend' && isset($_GET['id'])) {
    $customer_id = intval($_GET['id']);
    
    // Update customer status
    $update_sql = "UPDATE customer SET Status = 'suspended' WHERE CustomerID = $customer_id";
    
    if ($conn->query($update_sql)) {
        $message = "Customer suspended successfully!";
    } else {
        $error = "Error suspending customer: " . $conn->error;
    }
}

if (isset($_GET['action']) && $_GET['action'] == 'activate' && isset($_GET['id'])) {
    $customer_id = intval($_GET['id']);
    
    // Update customer status
    $update_sql = "UPDATE customer SET Status = 'active' WHERE CustomerID = $customer_id";
    
    if ($conn->query($update_sql)) {
        $message = "Customer activated successfully!";
    } else {
        $error = "Error activating customer: " . $conn->error;
    }
}

// Get filter parameters
$status_filter = isset($_GET['filter']) ? $_GET['filter'] : '';
$search = isset($_GET['search']) ? $conn->real_escape_string($_GET['search']) : '';

// Build query with filters
$customers_sql = "SELECT CustomerID, Name, Email, Phone, Address, Status, IsVerified, RegistrationDate FROM customer";

$where_clauses = array();

if (!empty($status_filter)) {
    $where_clauses[] = "Status = '$status_filter'";
}

if (!empty($search)) {
    $where_clauses[] = "(Name LIKE '%$search%' OR Email LIKE '%$search%' OR Phone LIKE '%$search%')";
}

if (!empty($where_clauses)) {
    $customers_sql .= " WHERE " . implode(" AND ", $where_clauses);
}

$customers_sql .= " ORDER BY RegistrationDate DESC";
$customers_result = $conn->query($customers_sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System - Manage Customers</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body>
    <div class="container">
        <header class="admin-header">
            <h1>Banking System Admin</h1>
            <div class="user-info">
                <p>Welcome, <?php echo htmlspecialchars($admin_name); ?></p>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </header>
        
        <nav class="admin-nav">
            <ul>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="customers.php" class="active">Manage Customers</a></li>
                <li><a href="accounts.php">Manage Accounts</a></li>
                <li><a href="transactions.php">Transactions</a></li>
                <li><a href="reports.php">Reports</a></li>
            </ul>
        </nav>
        
        <main class="admin-main">
            <h2>Manage Customers</h2>
            
            <?php if (!empty($message)): ?>
                <div class="success-message"><?php echo $message; ?></div>
            <?php endif; ?>
            
            <?php if (!empty($error)): ?>
                <div class="error-message"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <section class="filters">
                <form method="get" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="filter-form">
                    <div class="filter-group">
                        <label for="filter">Status:</label>
                        <select id="filter" name="filter" onchange="this.form.submit()">
                            <option value="">All Customers</option>
                            <option value="pending" <?php echo ($status_filter == 'pending') ? 'selected' : ''; ?>>Pending</option>
                            <option value="active" <?php echo ($status_filter == 'active') ? 'selected' : ''; ?>>Active</option>
                            <option value="suspended" <?php echo ($status_filter == 'suspended') ? 'selected' : ''; ?>>Suspended</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="search">Search:</label>
                        <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Name, Email or Phone">
                    </div>
                    
                    <button type="submit" class="btn">Apply Filters</button>
                    <a href="customers.php" class="btn btn-secondary">Reset</a>
                </form>
            </section>
            
            <section class="customers-list">
                <?php if ($customers_result->num_rows > 0): ?>
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Status</th>
                                <th>Verified</th>
                                <th>Registration Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($customer = $customers_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $customer['CustomerID']; ?></td>
                                    <td><?php echo htmlspecialchars($customer['Name']); ?></td>
                                    <td><?php echo htmlspecialchars($customer['Email']); ?></td>
                                    <td><?php echo htmlspecialchars($customer['Phone']); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $customer['Status']; ?>">
                                            <?php echo ucfirst($customer['Status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php echo $customer['IsVerified'] ? 'Yes' : 'No'; ?>
                                    </td>
                                    <td>
                                        <?php echo !empty($customer['RegistrationDate']) ? date('M d, Y', strtotime($customer['RegistrationDate'])) : 'N/A'; ?>
                                    </td>
                                    <td class="actions">
                                        <a href="customer_details.php?id=<?php echo $customer['CustomerID']; ?>" class="btn btn-sm">View</a>
                                        
                                        <?php if ($customer['Status'] == 'pending' && $customer['IsVerified']): ?>
                                            <a href="customers.php?action=approve&id=<?php echo $customer['CustomerID']; ?>" class="btn btn-sm btn-success" onclick="return confirm('Are you sure you want to approve this customer?')">Approve</a>
                                        <?php elseif ($customer['Status'] == 'active'): ?>
                                            <a href="customers.php?action=suspend&id=<?php echo $customer['CustomerID']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to suspend this customer?')">Suspend</a>
                                        <?php elseif ($customer['Status'] == 'suspended'): ?>
                                            <a href="customers.php?action=activate&id=<?php echo $customer['CustomerID']; ?>" class="btn btn-sm btn-success" onclick="return confirm('Are you sure you want to activate this customer?')">Activate</a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>No customers found matching your criteria.</p>
                <?php endif; ?>
            </section>
        </main>
        
        <footer>
            <p>&copy; <?php echo date("Y"); ?> Banking System. All rights reserved.</p>
        </footer>
    </div>
    
    <script src="../js/admin.js"></script>
</body>
</html>

<?php
$conn->close();
?>
